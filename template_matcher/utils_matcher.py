import cv2
import numpy as np
from typing import List, <PERSON>ple
import os

def extract_and_match_features(
        template_gray: np.n<PERSON><PERSON>, 
        scene_gray: np.ndar<PERSON>, 
        method: str = "SIFT"
        ) -> Tuple[List[cv2.DMatch], List[cv2.KeyPoint], List[cv2.KeyPoint]]:
    """
    Extract keypoints from two grayscale images, compute descriptors and
    perform brute-force matching using the specified feature extraction method.

    Args:
        template_gray: np.ndarray
            Grayscale template image.
        scene_gray: np.ndarray
            Grayscale scene image.
        method: str
            Feature extraction method ("ORB" or "SIFT").

    Returns:
        matches: list[cv2.DMatch] | None
            List of matched features sorted by distance. Returns ``None`` if
            insufficient keypoints/descriptors are detected.
        kp1: list[cv2.KeyPoint]
            Keypoints detected in the template image.
        kp2: list[cv2.KeyPoint]
            Keypoints detected in the scene image.

    Raises:
        ValueError: If the feature extraction method is not supported.
    """
    method = method.upper()

    if method == "ORB":
        detector = cv2.ORB_create()
        norm_type = cv2.NORM_HAMMING
    elif method == "SIFT":
        detector = cv2.SIFT_create()
        norm_type = cv2.NORM_L2
    else:
        raise ValueError(f"Unsupported feature extraction method '{method}'. Use 'ORB' or 'SIFT'.")

    print(f"{method} feature detector initialized.")
    

    # Detect keypoints and compute descriptors
    kp1, des1 = detector.detectAndCompute(template_gray, None)
    kp2, des2 = detector.detectAndCompute(scene_gray, None)
    print(f"Found {len(kp1)} keypoints in the template.")
    print(f"Found {len(kp2)} keypoints in the scene.")

    # Validate descriptors
    if des1 is None or des2 is None or len(des1) < 2 or len(des2) < 2:
        print("Error: Not enough keypoints detected for matching. Ensure image content is rich enough.")
        return None, kp1, kp2

    # Perform brute-force matching
    bf = cv2.BFMatcher(norm_type, crossCheck=True)
    print("BFMatcher initialized.")
    matches = bf.match(des1, des2)
    print(f"Found {len(matches)} initial matches.")

    # Sort matches by distance (best matches first)
    matches = sorted(matches, key=lambda x: x.distance)

    return matches, kp1, kp2


def detect_circle_in_roi(
        roi_gray: np.ndarray, 
        x_offset: int, 
        y_offset: int, 
        flag: str = "small"
        ) -> List[Tuple[int, int, int]]:
    """
    Detect circles in ROI and return all detected circles with confidence metrics.
    
    Args:
        roi_gray: np.ndarray
            Grayscale ROI image for circle detection
        x_offset : int
            X offset to convert ROI coordinates to scene coordinates
        y_offset : int
            Y offset to convert ROI coordinates to scene coordinates
        flag: str
            Flag to select the circle size ("small" or "large").
    Returns:
        List[Tuple[int, int, int]]
            List of detected circles as (center_x, center_y, radius) in scene coordinates.
            Returns empty list if no circles detected or invalid ROI.
    """
    if roi_gray.shape[0] <= 0 or roi_gray.shape[1] <= 0:
        print("ROI is empty, cannot perform Hough Circle detection.")
        return []
        
    # Apply Hough Circle Transform
    if flag == "small":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                    param1=100, param2=50, minRadius=70, maxRadius=90) # small circle
    elif flag == "large":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                    param1=60, param2=50, minRadius=150, maxRadius=185) # large circle # 80 170 185
    else:
        raise ValueError(f"Unsupported flag '{flag}'. Use 'small' or 'large'.")
    
    if circles is None:
        print("No circles detected within the matched region.")
        return []
        
    circles = np.uint16(np.around(circles))
    detected_circles = []
    
    # Convert circles to scene coordinates
    for circle in circles[0, :]:
        center_x = int(circle[0]) + x_offset
        center_y = int(circle[1]) + y_offset  
        radius = int(circle[2])

        detected_circles.append((center_x, center_y, radius))
        
    return detected_circles


def preprocess_image(image, is_template=False):
    """
    Image preprocessing: grayscale + Gaussian blur + adaptive threshold
    Args:
        image: np.ndarray
            Image to be preprocessed.
        is_template: bool
            Whether the image is a template image.
    Returns:
        np.ndarray: Preprocessed image.
    """
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    thresh = cv2.adaptiveThreshold(
        blurred, 255, 
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY_INV,  21, 10 # 15, 0 # 21, 10  21, 2 # 
    )
    
    if is_template:
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    return thresh


def template_matching_registration(template_path, image_path, save_dir="template_matcher/output/0714", flag="small"):
    """
    Template matching + affine transformation registration (fixed version)
    Args:
        template_path: str
            Path to the template image.
        image_path: str
            Path to the image.
        save_dir: str
            Path to the save directory.
        flag: str
            Flag to select the circle size ("small" or "large").
    Returns:
        List[Tuple[int, int]]: List of detected circles as (center_x, center_y) in scene coordinates.
    """

    # create save_dir if not exists
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 1. Load images
    template_bgr = cv2.imread(template_path)
    image_bgr = cv2.imread(image_path)
    
    # 2. Preprocessing
    template_gray = preprocess_image(template_bgr, is_template=True)
    image_gray = preprocess_image(image_bgr)
   # cv2.imwrite(f"{save_dir}/template_gray_{flag}.png", template_gray)
   # cv2.imwrite(f"{save_dir}/image_gray_{flag}.png", image_gray)

    # 3. Template matching
    result = cv2.matchTemplate(
        image_gray, template_gray, 
        cv2.TM_CCOEFF_NORMED
    )
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    image_gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
    # 4. Determine matched region
    h, w = template_gray.shape[:2]
    top_left = max_loc
    bottom_right = (top_left[0] + w, top_left[1] + h)
    cv2.rectangle(image_bgr, top_left, bottom_right, (0 ,0 ,250 ), 3)
    # cv2.imwrite(f"{save_dir}/image_bgr_{flag}.png", image_bgr)
    cv2.imshow("Image", image_bgr)
    cv2.waitKey(0)

    # Extract ROI from the original scene image (grayscale for HoughCircles)
    roi_gray = image_gray[top_left[1]:top_left[1] + h - 1, top_left[0]:top_left[0] + w - 1]

    # Detect circles using the utility function
    detected_circles = detect_circle_in_roi(
        roi_gray=roi_gray,
        x_offset=top_left[0],
        y_offset=top_left[1],
        flag=flag
    )

    circles_res = []
    if detected_circles:
        for center_x, center_y, radius in detected_circles:
            # Draw outer circle
            cv2.circle(image_bgr, (center_x, center_y), radius, (0, 0, 255), 3)  # Red circle
            # Draw center of the circle
            cv2.circle(image_bgr, (center_x, center_y), 2, (0, 255, 0), 5)  # Green center
            print(f"Detected circle at ({center_x}, {center_y}) with radius {radius} within the bounded region.")
            circles_res.append((center_x, center_y))

    # cv2.imwrite(f"{save_dir}/image_{flag}.png", image_bgr)
    cv2.imshow("Image", image_bgr)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

    return circles_res
