import cv2
import numpy as np
import math

def draw_line_and_angle(p1, p2, image_size=(3648, 6021, 3)):
    """
    在一个空白图像上根据两个点绘制一条直线，并计算其与水平线的夹角。

    参数:
    p1 (tuple): 第一个点的坐标 (x1, y1)。
    p2 (tuple): 第二个点的坐标 (x2, y2)。
    image_size (tuple): 图像的尺寸 (高度, 宽度, 通道数)。

    返回:
    numpy.ndarray: 绘制了直线和角度的图像。
    float: 计算出的角度（单位：度）。
    """
    # 创建一个黑色的空白图像
    img = np.zeros(image_size, dtype=np.uint8)

    # --- 绘制 ---
    # 1. 绘制原始直线 (白色)
    cv2.line(img, p1, p2, (255, 255, 255), 2)

    # 2. 从 p1 点绘制一条水平参考线 (灰色)
    # 水平线的终点 x 坐标可以任意设置，这里设为图像宽度-50，以保证可见
    # p_horizontal_right = (image_size[1] - 50, p1[1])
    # cv2.line(img, p1, p_horizontal_right, (128, 128, 128), 1, cv2.LINE_AA) # 使用抗锯齿让细线更平滑
    p_horizontal_left = (50, p1[1])
    cv2.line(img, p1, p_horizontal_left, (128, 128, 128), 1, cv2.LINE_AA) # 使用抗锯齿让细线更平滑

    # --- 计算角度 ---
    # 计算 x 和 y 的差值
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]

    # 使用 atan2 计算弧度。
    # 在OpenCV坐标系中，y轴向下为正，所以我们用 -dy 来匹配标准数学坐标系（y轴向上为正）
    angle_rad = math.atan2(-dy, dx)

    # 将弧度转换为度
    angle_deg = math.degrees(angle_rad) + 180

    # --- 显示角度文本 ---
    text = f"Angle: {angle_deg:.2f} degrees"
    # 将文本放置在 p1 附近
    text_position = (p1[0] + 15, p1[1] - 15)
    cv2.putText(img, text, text_position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    return img, angle_deg

# --- 主程序 ---
if __name__ == '__main__':
    # 定义两个像素点的坐标
    point1 = (3301, 1876)
    point2 = (2989, 2045)

    # 调用函数生成带直线的图像并获取角度
    image_with_line, calculated_angle = draw_line_and_angle(point1, point2)

    # 在控制台打印结果
    print(f"点1: {point1}")
    print(f"点2: {point2}")
    print(f"计算出的角度为: {calculated_angle:.2f} 度")
    print("请按任意键退出...")

    cv2.imwrite("line_and_angle.png", image_with_line)
    # --- 显示图像 ---
    window_name = 'Line and Angle'
    cv2.imshow(window_name, image_with_line)

    # 等待用户按键，然后关闭窗口
    cv2.waitKey(0)
    cv2.destroyAllWindows()
