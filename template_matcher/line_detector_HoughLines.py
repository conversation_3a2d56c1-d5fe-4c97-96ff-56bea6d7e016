#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
probabilistic_hough_lines.py
Detect straight lines with the Probabilistic Hough Transform.

usage: python probabilistic_hough_lines.py --input input.jpg --output out.jpg
"""
import cv2
import argparse
import numpy as np
import math
from pathlib import Path

def calculate_rotated_vector_endpoint(angle_deg, reference_point):
    """
    Calculate rotated unit vector endpoint from reference point.
    
    Args:
        angle_deg (float): Rotation angle in degrees
        reference_point (tuple): Reference point (x, y)
    
    Returns:
        tuple: Rotated vector endpoint coordinates (x, y)
    """
    if reference_point is None:
        return None
    
    ref_x, ref_y = reference_point
    
    # Create unit vector along positive x-axis: (1, 0)
    unit_vector = np.array([1.0, 0.0])
    
    # Rotation matrix for counter-clockwise rotation
    angle_rad = math.radians(angle_deg)
    cos_angle = math.cos(angle_rad)
    sin_angle = math.sin(angle_rad)
    rotation_matrix = np.array([[cos_angle, -sin_angle],
                              [sin_angle, cos_angle]])
    
    # Apply rotation to unit vector
    rotated_vector = rotation_matrix @ unit_vector
    
    # Calculate endpoint coordinates from reference point
    rotated_vector_endpoint = (ref_x + rotated_vector[0], ref_y + rotated_vector[1])
    
    print(f"Reference point: ({ref_x}, {ref_y})")
    print(f"Rotated unit vector: ({rotated_vector[0]:.4f}, {rotated_vector[1]:.4f})")
    print(f"Rotated vector endpoint: ({rotated_vector_endpoint[0]:.4f}, {rotated_vector_endpoint[1]:.4f})")
    
    return rotated_vector_endpoint


def detect_lines(img_bgr,
                 canny_thresh1=50,
                 canny_thresh2=150,
                 hough_rho=1,
                 hough_theta=np.pi/180,
                 hough_thresh=80):
    """Return a copy of img with detected line segments drawn and list of horizontal line angles."""
    gray   = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    edges  = cv2.Canny(gray, canny_thresh1, canny_thresh2)
    cv2.imwrite("edges.png", edges)
    
    # Use HoughLines instead of HoughLinesP
    # Returns lines in polar coordinates (rho, theta)
    lines = cv2.HoughLines(edges,
                          rho=hough_rho,
                          theta=hough_theta,
                          threshold=hough_thresh)

    vis = img_bgr.copy()
    horizontal_angles = []
    
    if lines is not None:
        print(f"Detected {len(lines)} lines")
        
        # Filter lines by angle - keep only horizontal lines (±10 degrees)
        filtered_lines = []
        
        for line in lines:
            rho, theta = line[0]
            
            # Convert theta to degrees
            theta_deg = math.degrees(theta)
            
            # Check if line is horizontal (±10 degrees)
            # theta is the angle between the line's normal vector and x-axis; the angle between the line and x-axis is 90-theta
            is_horizontal = (theta_deg >= 80) and (theta_deg <= 100) 
            
            if is_horizontal:
                filtered_lines.append((rho, theta))
                
                # Calculate angle with horizontal (in degrees)
                # For horizontal lines, the angle is theta itself (adjusted for coordinate system)
                angle_with_horizontal = 90 - theta_deg
                horizontal_angles.append(angle_with_horizontal)
                
                # Convert polar coordinates to Cartesian for visualization
                a = np.cos(theta)
                b = np.sin(theta)
                x0 = a * rho
                y0 = b * rho
                
                # Calculate line endpoints for drawing
                x1 = int(x0 + 1000 * (-b))
                y1 = int(y0 + 1000 * (a))
                x2 = int(x0 - 1000 * (-b))
                y2 = int(y0 - 1000 * (a))
                
                # Draw the line
                cv2.line(vis, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                print(f"Horizontal line: rho={rho:.2f}, theta={theta_deg:.2f}°, angle_with_horizontal={angle_with_horizontal:.2f}°")
        
        print(f"Filtered {len(filtered_lines)} horizontal lines from {len(lines)} total lines")
        
    else:
        print("No lines detected")
    
    return vis, horizontal_angles



def get_angle(x1, y1, x2, y2):
    dx = x2 - x1
    dy = y2 - y1
    angle_rad = math.atan2(dy, dx)  # 弧度
    angle_deg = math.degrees(angle_rad)  # 转换为度
    return angle_rad, angle_deg



def calculate_robot_rx_from_pixel_rotation(
    p_target_pix, p_center_pix, angle_a_deg, M
):
    """
    Calculate robot Rx rotation angle based on 2D rotation in pixel coordinate system.

    Args:
        p_target_pix (tuple): Target point's pixel coordinates (x, y) to be rotated.
        p_center_pix (tuple): Rotation center's pixel coordinates (x, y).
        angle_a_deg (float): Rotation angle in degrees (positive for counter-clockwise).
        M (np.ndarray): 3x3 transformation matrix from pixel to robot base coordinates.

    Returns:
        float: Calculated robot Rx rotation angle in degrees.
    """
    print("--- Calculation started ---")
    
    # --- Step 1: Define rotation in pixel coordinate system (input parameters) ---
    # Convert input tuples to NumPy arrays for calculation
    p_target = np.array(p_target_pix, dtype=float)
    p_center = np.array(p_center_pix, dtype=float)
    print(f"Step 1: Rotation center (pixels) = {p_center}, Target point (pixels) = {p_target}")

    # --- Step 2: Calculate rotated new point (pixel coordinates) ---
    print(f"\nStep 2: Rotating target point by {angle_a_deg} degrees in pixel plane...")
    
    # Convert rotation angle from degrees to radians
    angle_a_rad = np.deg2rad(angle_a_deg)
    
    # Create 2D rotation matrix
    # Note: In image coordinates, y-axis typically points downward, so standard CCW rotation matrix may need adjustment
    # But here we follow mathematical convention where CCW is positive
    # rot_matrix_2d = np.array([
    #     [np.cos(angle_a_rad), -np.sin(angle_a_rad)],
    #     [np.sin(angle_a_rad),  np.cos(angle_a_rad)]
    # ])
    rot_matrix_2d = np.array([
        [np.cos(angle_a_rad),  np.sin(angle_a_rad)],
        [-np.sin(angle_a_rad), np.cos(angle_a_rad)]
    ])
    
    # Apply standard 2D rotation formula: p' = R * (p - c) + c
    # 1. Translate coordinate system to rotation center
    p_translated = p_target - p_center
    # 2. Perform rotation
    p_rotated = rot_matrix_2d @ p_translated
    # 3. Translate coordinate system back
    p_new = p_rotated + p_center
    
    print(f"Rotated new target point (pixels) = {p_new}")

    # --- Step 3: Convert key points from pixel to robot base coordinates ---
    print("\nStep 3: Converting pixel points to 3D base coordinates using transformation matrix M...")
    
    # Convert 2D pixel points to homogeneous coordinates (x, y, 1) for 3x3 matrix multiplication
    p_center_hom = np.append(p_center, 1) # (u, v, 1)
    p_target_hom = np.append(p_target, 1)
    p_new_hom = np.append(p_new, 1)
    
    # Perform matrix multiplication
    v_center_base = M @ p_center_hom # (y, z, 1)
    v_target_base = M @ p_target_hom
    v_new_base = M @ p_new_hom

    v_center_base = np.array([0.76996, v_center_base[0], v_center_base[1]])
    v_target_base = np.array([0.76996, v_target_base[0], v_target_base[1]])
    v_new_base = np.array([0.76996, v_new_base[0], v_new_base[1]])
    
    print(f"Rotation center (base coords) = {v_center_base}")
    print(f"Original target (base coords) = {v_target_base}")
    print(f"Rotated new point (base coords) = {v_new_base}")
    
    # --- Step 4: Calculate true rotation angle Rx in 3D space ---
    print("\nStep 4: Calculating final Rx angle in 3D base coordinate system...")
    
    # 1. Define 3D vectors from rotation center to target points
    vec1 = v_target_base - v_center_base
    vec2 = v_new_base - v_center_base
    print(f"Pre-rotation 3D vector (v1) = {vec1}")
    print(f"Post-rotation 3D vector (v2) = {vec2}")

    # 2. Calculate angle between vectors in Y-Z plane
    # Using atan2(z, y) because Rx is rotation around X-axis
    # Standard atan2(y, x) but here we're rotating around X-axis so plane is Y-Z
    theta1_rad = np.arctan2(vec1[2], vec1[1]) # atan2(z, y)
    theta2_rad = np.arctan2(vec2[2], vec2[1]) # atan2(z, y)

    # Calculate angle difference
    rx_rad = theta2_rad - theta1_rad
    
    # Convert radians to degrees
    rx_deg = np.rad2deg(rx_rad)
    
    print(f"\n--- Calculation completed ---")
    print(f"Pixel plane rotation angle: {angle_a_deg:.2f} degrees")
    print(f"Calculated robot Rx rotation angle: {rx_deg:.2f} degrees")
    print(f"Calculated robot Rx rotation angle: {rx_rad:.2f} radians")
    
    return rx_deg, rx_rad, v_new_base


def rotate_point_in_yz_plane(tcp_center, point_p, angle_degrees):
    """
    在给定的y-z平面内，将一个点P围绕另一个点TCP旋转指定的角度。

    Args:
        tcp_center (tuple): 旋转中心点（TCP）的坐标 (x0, y0, z0)。
        point_p (tuple): 需要被旋转的点P的坐标 (x0, y1, z1)。
        angle_degrees (float): 旋转的角度（单位：度）。正值表示逆时针旋转。

    Returns:
        tuple: 旋转后点P'的新坐标 (x_new, y_new, z_new)。
               如果输入的x坐标不一致，则返回None。
    """
    x0, y0, z0 = tcp_center
    x1, y1, z1 = point_p

    # 检查两个点是否在同一个y-z平面上
    if x0 != x1:
        print("错误：TCP中心点和点P必须具有相同的x坐标才能在y-z平面内旋转。")
        return None

    # --- 步骤 1: 将坐标系平移，使旋转中心(y0, z0)移动到原点(0,0) ---
    p_translated_y = y1 - y0
    p_translated_z = z1 - z0

    # --- 步骤 2: 将旋转角度从度转换为弧度 ---
    # Python的math库中的三角函数使用弧度
    angle_radians = math.radians(angle_degrees)

    # --- 步骤 3: 应用标准二维旋转公式 ---
    # y' = y*cos(a) - z*sin(a)
    # z' = y*sin(a) + z*cos(a)
    p_rotated_y = p_translated_y * math.cos(angle_radians) - p_translated_z * math.sin(angle_radians)
    p_rotated_z = p_translated_y * math.sin(angle_radians) + p_translated_z * math.cos(angle_radians)

    # --- 步骤 4: 将坐标系平移回去 ---
    # 将旋转后的点从原点移回到原来的旋转中心
    final_y = p_rotated_y + y0
    final_z = p_rotated_z + z0
    
    # x坐标保持不变
    final_x = x0

    return (final_x, final_y, final_z)

def calculate_angle_with_horizontal(tcp_center, point_p):
    """
    在y-z平面内，计算从TCP指向P的向量与水平轴（Y轴正方向）的夹角。

    Args:
        tcp_center (tuple): TCP中心点的坐标 (x0, y0, z0)。
        point_p (tuple): 点P的坐标 (x0, y1, z1)。

    Returns:
        float: 返回计算出的角度（单位：度）。范围在 -180 到 180 之间。
               如果输入的x坐标不一致，则返回None。
    """
    x0, y0, z0 = tcp_center
    x1, y1, z1 = point_p

    # 再次检查两个点是否在同一个y-z平面上
    if x0 != x1:
        print("错误：TCP中心点和点P必须具有相同的x坐标。")
        return None
        
    # 计算向量的分量
    delta_y = y1 - y0
    delta_z = z1 - z0

    # 使用atan2计算弧度。atan2(z, y)可以处理所有象限
    angle_radians = math.atan2(delta_z, delta_y)

    # 将弧度转换为度
    angle_degrees = math.degrees(angle_radians)

    return angle_degrees




def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('-i', '--input',  required=True, help='input image path')
    ap.add_argument('-o', '--output', required=True, help='output image path')
    args = ap.parse_args()

    img = cv2.imread(args.input)
    if img is None:
        raise FileNotFoundError(f'Cannot read {args.input}')

    # {{ AURA: Modify - Update to handle new return values from detect_lines }}
    result, angles = detect_lines(img)  

    # save + print
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    cv2.imwrite(args.output, result)
    print(f'Saved: {args.output}')
    
    # {{ AURA: Modify - Print list of angles instead of single angle }}
    if angles:
        print(f'Detected {len(angles)} horizontal lines with angles:')
        for i, angle in enumerate(angles):
            print(f'  Line {i+1}: {angle:.2f} degrees')
    else:
        print('No horizontal lines detected')

if __name__ == '__main__':
    main()
