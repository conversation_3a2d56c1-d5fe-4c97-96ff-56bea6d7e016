#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
probabilistic_hough_lines.py
Detect straight lines with the Probabilistic Hough Transform.

usage: python probabilistic_hough_lines.py --input input.jpg --output out.jpg
"""
import cv2
import argparse
import numpy as np
import math
from pathlib import Path

def detect_lines(img_bgr,
                 canny_thresh1=50,
                 canny_thresh2=150,
                 hough_rho=1,
                 hough_theta=np.pi/180,
                 hough_thresh=80,
                 min_line_len=100,
                 max_line_gap=5,
                 reference_point=None):
    """Return a copy of img with detected line segments drawn, longest line angle, and rotated unit vector endpoint."""
    gray   = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    edges  = cv2.Canny(gray, canny_thresh1, canny_thresh2)
    cv2.imwrite("edges.png", edges)
    # (x1,y1,x2,y2) for each detected segment
    lines = cv2.HoughLinesP(edges,
                            rho=hough_rho,
                            theta=hough_theta,
                            threshold=hough_thresh,
                            minLineLength=min_line_len,
                            maxLineGap=max_line_gap)  # OpenCV PPHT[[1]]

    vis = img_bgr.copy()
    longest_line_angle = 0
    rotated_vector_endpoint = None
    
    if lines is not None:
        # Print debug information
        print(f"Detected {len(lines)} line segments")
        
        # {{ AURA: Modify - Optimize loop to find longest line first, then calculate angle only once }}
        # First pass: find the longest line segment
        max_length = 0
        longest_line = None
        
        for x1, y1, x2, y2 in lines[:, 0]:
            # Draw all detected lines
            cv2.line(vis, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Print line coordinates for debugging
            print(f"Line: ({x1}, {y1}) -> ({x2}, {y2})")
            
            # Calculate line length
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            
            # Track longest line
            if length > max_length:
                max_length = length
                longest_line = (x1, y1, x2, y2)
        
        # {{ AURA: Modify - Calculate angle only for the longest line }}
        # Second pass: calculate angle only for the longest line
        if longest_line is not None:
            x1, y1, x2, y2 = longest_line
            
            # Calculate angle with horizontal (in degrees)
            dx = x2 - x1
            dy = y2 - y1
            angle_rad = math.atan2(-dy, dx)  # Negative dy because y increases downward
            longest_line_angle = math.degrees(angle_rad) # positive is counter-clockwise, negative is clockwise
            modified_angle = 28.44 - longest_line_angle

            # Print longest line information
            print(f"Longest line: ({x1}, {y1}) -> ({x2}, {y2})")
            print(f"Length: {max_length:.2f} pixels")
            print(f"Angle with horizontal: {longest_line_angle:.2f} degrees")
            print(f"Modified angle: {modified_angle:.2f} degrees")
            
            # {{ AURA: Add - Vector rotation functionality }}
            # Calculate rotated unit vector endpoint if reference point is provided
            if reference_point is not None:
                ref_x, ref_y = reference_point
                
                # Create unit vector along positive x-axis: (1, 0)
                unit_vector = np.array([1.0, 0.0])
                
                # Rotation matrix for counter-clockwise rotation
                angle_rad = math.radians(longest_line_angle)
                cos_angle = math.cos(angle_rad)
                sin_angle = math.sin(angle_rad)
                rotation_matrix = np.array([[cos_angle, -sin_angle],
                                          [sin_angle, cos_angle]])
                
                # Apply rotation to unit vector
                rotated_vector = rotation_matrix @ unit_vector
                
                # Calculate endpoint coordinates from reference point
                rotated_vector_endpoint = (ref_x + rotated_vector[0], ref_y + rotated_vector[1])
                
                print(f"Reference point: ({ref_x}, {ref_y})")
                print(f"Rotated unit vector: ({rotated_vector[0]:.4f}, {rotated_vector[1]:.4f})")
                print(f"Rotated vector endpoint: ({rotated_vector_endpoint[0]:.4f}, {rotated_vector_endpoint[1]:.4f})")
    else:
        print("No lines detected")
    
    return vis, modified_angle, rotated_vector_endpoint


def calculate_robot_rx_from_pixel_rotation(
    p_target_pix, p_center_pix, angle_a_deg, M
):
    """
    Calculate robot Rx rotation angle based on 2D rotation in pixel coordinate system.

    Args:
        p_target_pix (tuple): Target point's pixel coordinates (x, y) to be rotated.
        p_center_pix (tuple): Rotation center's pixel coordinates (x, y).
        angle_a_deg (float): Rotation angle in degrees (positive for counter-clockwise).
        M (np.ndarray): 3x3 transformation matrix from pixel to robot base coordinates.

    Returns:
        float: Calculated robot Rx rotation angle in degrees.
    """
    print("--- Calculation started ---")
    
    # --- Step 1: Define rotation in pixel coordinate system (input parameters) ---
    # Convert input tuples to NumPy arrays for calculation
    p_target = np.array(p_target_pix, dtype=float)
    p_center = np.array(p_center_pix, dtype=float)
    print(f"Step 1: Rotation center (pixels) = {p_center}, Target point (pixels) = {p_target}")

    # --- Step 2: Calculate rotated new point (pixel coordinates) ---
    print(f"\nStep 2: Rotating target point by {angle_a_deg} degrees in pixel plane...")
    
    # Convert rotation angle from degrees to radians
    angle_a_rad = np.deg2rad(angle_a_deg)
    
    # Create 2D rotation matrix
    # Note: In image coordinates, y-axis typically points downward, so standard CCW rotation matrix may need adjustment
    # But here we follow mathematical convention where CCW is positive
    rot_matrix_2d = np.array([
        [np.cos(angle_a_rad), -np.sin(angle_a_rad)],
        [np.sin(angle_a_rad),  np.cos(angle_a_rad)]
    ])
    
    # Apply standard 2D rotation formula: p' = R * (p - c) + c
    # 1. Translate coordinate system to rotation center
    p_translated = p_target - p_center
    # 2. Perform rotation
    p_rotated = rot_matrix_2d @ p_translated
    # 3. Translate coordinate system back
    p_new = p_rotated + p_center
    
    print(f"Rotated new target point (pixels) = {p_new}")

    # --- Step 3: Convert key points from pixel to robot base coordinates ---
    print("\nStep 3: Converting pixel points to 3D base coordinates using transformation matrix M...")
    
    # Convert 2D pixel points to homogeneous coordinates (x, y, 1) for 3x3 matrix multiplication
    p_center_hom = np.append(p_center, 1)
    p_target_hom = np.append(p_target, 1)
    p_new_hom = np.append(p_new, 1)
    
    # Perform matrix multiplication
    v_center_base = M @ p_center_hom
    v_target_base = M @ p_target_hom
    v_new_base = M @ p_new_hom
    
    print(f"Rotation center (base coords) = {v_center_base}")
    print(f"Original target (base coords) = {v_target_base}")
    print(f"Rotated new point (base coords) = {v_new_base}")
    
    # --- Step 4: Calculate true rotation angle Rx in 3D space ---
    print("\nStep 4: Calculating final Rx angle in 3D base coordinate system...")
    
    # 1. Define 3D vectors from rotation center to target points
    vec1 = v_target_base - v_center_base
    vec2 = v_new_base - v_center_base
    print(f"Pre-rotation 3D vector (v1) = {vec1}")
    print(f"Post-rotation 3D vector (v2) = {vec2}")

    # 2. Calculate angle between vectors in Y-Z plane
    # Using atan2(z, y) because Rx is rotation around X-axis
    # Standard atan2(y, x) but here we're rotating around X-axis so plane is Y-Z
    theta1_rad = np.arctan2(vec1[2], vec1[1]) # atan2(z, y)
    theta2_rad = np.arctan2(vec2[2], vec2[1]) # atan2(z, y)

    # Calculate angle difference
    rx_rad = theta2_rad - theta1_rad
    
    # Convert radians to degrees
    rx_deg = np.rad2deg(rx_rad)
    
    print(f"\n--- Calculation completed ---")
    print(f"Pixel plane rotation angle: {angle_a_deg:.2f} degrees")
    print(f"Calculated robot Rx rotation angle: {rx_deg:.2f} degrees")
    print(f"Calculated robot Rx rotation angle: {rx_rad:.2f} radians")
    
    return rx_deg, rx_rad


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('-i', '--input',  required=True, help='input image path')
    ap.add_argument('-o', '--output', required=True, help='output image path')
    args = ap.parse_args()

    img = cv2.imread(args.input)
    if img is None:
        raise FileNotFoundError(f'Cannot read {args.input}')

    # {{ AURA: Modify - Update to handle new return values from detect_lines }}
    result, angle, rotated_endpoint = detect_lines(img)  

    # save + print
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    cv2.imwrite(args.output, result)
    print(f'Saved: {args.output}')
    print(f'Angle: {angle:.2f} degrees')
    
    # {{ AURA: Add - Print rotated vector endpoint if available }}
    if rotated_endpoint is not None:
        print(f'Rotated vector endpoint: ({rotated_endpoint[0]:.4f}, {rotated_endpoint[1]:.4f})')
    else:
        print('No rotated vector endpoint (no reference point provided)')

if __name__ == '__main__':
    main()
