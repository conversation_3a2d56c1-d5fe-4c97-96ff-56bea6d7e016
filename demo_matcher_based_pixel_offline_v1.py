import numpy as np

from robo_ctl.ur_robot_controller import URRobotController
from utils import pose_6d_to_matrix, matrix_to_axis_6d
from template_matcher.utils_rot import calculate_angle_with_horizontal
from template_matcher.utils_matcher import template_matching_registration


def get_user_confirmation():
    """Get user confirmation to move the robot."""
    user_input = input("Move the robot? (y/n): ").lower()
    if user_input == 'n':
        print("Operation cancelled.")
        exit()
    elif user_input != 'y':
        print("Invalid input!")
        exit()
    print("Robot moving!")

def move_robot(use_rot, world_delta=None, rot_angle=None):
    """Move the robot based on rotation mode and calculated parameters."""
    with URRobotController("192.168.25.15") as robot:
        if use_rot:
            # Initial position
            take_pic_euler = np.array([0.76383, -0.24167, 0.36874, 180, -0.0, -90.0])
            take_pic_matrix = pose_6d_to_matrix(take_pic_euler, unit='m')
            take_pic_axis = matrix_to_axis_6d(take_pic_matrix, unit='m')

            # Translation to align centers
            take_pic_axis[0] -= 0.15
            robot.move_linear(take_pic_axis, 0.05, 0.05)

            take_pic_axis[0] += 0.15
            take_pic_axis[1] = -0.1
            take_pic_axis[2] = 0.332
            Rx, Ry, Rz = take_pic_euler[3], take_pic_euler[4] + rot_angle, take_pic_euler[5]
            target_euler = np.array([take_pic_axis[0], take_pic_axis[1], take_pic_axis[2], Rx, Ry, Rz])
            target_matrix = pose_6d_to_matrix(target_euler, unit='m')
            target_axis = matrix_to_axis_6d(target_matrix, unit='m')
            robot.move_linear(target_axis, 0.05, 0.05)
        else:
            position = robot.get_tcp_pose()
            position[0] -= 0.15
            robot.move_linear(position, 0.05, 0.05)

            position[0] += 0.12
            position[1] += world_delta[0]
            position[2] += world_delta[1]
            robot.move_linear(position, 0.05, 0.05)

def main():
    scene_path = "data/2d_images/0710_T4/Image_20250721143505006.bmp"
    large_template = "data/2d_images/template/temp_large.jpg"
    small_template = "data/2d_images/template/temp_small.jpg"
    rot_template = "data/2d_images/template/temp_rot.png"

    use_rot = False  # Set to True to enable rotation mode

    # Perform template matching
    large_template_path = rot_template if use_rot else large_template
    large_center = template_matching_registration(large_template_path, scene_path, "large")[0]
    small_center = template_matching_registration(small_template, scene_path, "small")[0]

    print(f"Large circle center: {large_center}")
    print(f"Small circle center: {small_center}")

    # Transformation matrix (camera to world)
    R_cam_to_world = np.array([
        [-3.05701394e-06, 6.36506259e-05, -3.98354809e-01],
        [-6.31956155e-05, -4.21291944e-06, 2.21344384e-01],
        [0, 0, 1]
    ])

    # Unused matrix - kept for compatibility
    R_world_to_cam = np.array([
        [ -1044.01138588, -15773.37975983,   3075.46206763],
        [ 15660.62279347,   -757.56429944,   6406.1670116 ],
        [0, 0, 1]
    ])

    large_world = R_cam_to_world @ np.array([large_center[0], large_center[1], 1])
    small_world = R_cam_to_world @ np.array([small_center[0], small_center[1], 1])
    world_delta = small_world - large_world

    print(f"Large world coordinates: {large_world}")
    print(f"World delta: dx=0, dy={world_delta[0]:.4f}, dz={world_delta[1]:.4f}")

    rot_angle = None
    if use_rot:
        tcp_xyz = np.array([0.76383, -0.24167, 0.36874])
        large_center_xyz = np.array([tcp_xyz[0], large_world[0], large_world[1]])
        initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_center_xyz)
        rot_angle = - (90 + 28.44) - initial_angle
        print(f"Large center XYZ: {large_center_xyz}")
        print(f"Initial angle: {initial_angle:.4f} degrees")
        print("-" * 30)
        print(f"Rotation angle: {rot_angle:.2f} degrees")

    get_user_confirmation()
    move_robot(use_rot, world_delta, rot_angle)

if __name__ == "__main__":
    main()
