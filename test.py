import math

def rotate_point_in_yz_plane(tcp_center, point_p, angle_degrees):
    """
    在给定的y-z平面内，将一个点P围绕另一个点TCP旋转指定的角度。

    Args:
        tcp_center (tuple): 旋转中心点（TCP）的坐标 (x0, y0, z0)。
        point_p (tuple): 需要被旋转的点P的坐标 (x0, y1, z1)。
        angle_degrees (float): 旋转的角度（单位：度）。正值表示逆时针旋转。

    Returns:
        tuple: 旋转后点P'的新坐标 (x_new, y_new, z_new)。
               如果输入的x坐标不一致，则返回None。
    """
    x0, y0, z0 = tcp_center
    x1, y1, z1 = point_p

    # 检查两个点是否在同一个y-z平面上
    if x0 != x1:
        print("错误：TCP中心点和点P必须具有相同的x坐标才能在y-z平面内旋转。")
        return None

    # --- 步骤 1: 将坐标系平移，使旋转中心(y0, z0)移动到原点(0,0) ---
    p_translated_y = y1 - y0
    p_translated_z = z1 - z0

    # --- 步骤 2: 将旋转角度从度转换为弧度 ---
    # Python的math库中的三角函数使用弧度
    angle_radians = math.radians(angle_degrees)

    # --- 步骤 3: 应用标准二维旋转公式 ---
    # y' = y*cos(a) - z*sin(a)
    # z' = y*sin(a) + z*cos(a)
    p_rotated_y = p_translated_y * math.cos(angle_radians) - p_translated_z * math.sin(angle_radians)
    p_rotated_z = p_translated_y * math.sin(angle_radians) + p_translated_z * math.cos(angle_radians)

    # --- 步骤 4: 将坐标系平移回去 ---
    # 将旋转后的点从原点移回到原来的旋转中心
    final_y = p_rotated_y + y0
    final_z = p_rotated_z + z0
    
    # x坐标保持不变
    final_x = x0

    return (final_x, final_y, final_z)

def calculate_angle_with_horizontal(tcp_center, point_p):
    """
    在y-z平面内，计算从TCP指向P的向量与水平轴（Y轴正方向）的夹角。

    Args:
        tcp_center (tuple): TCP中心点的坐标 (x0, y0, z0)。
        point_p (tuple): 点P的坐标 (x0, y1, z1)。

    Returns:
        float: 返回计算出的角度（单位：度）。范围在 -180 到 180 之间。
               如果输入的x坐标不一致，则返回None。
    """
    x0, y0, z0 = tcp_center
    x1, y1, z1 = point_p

    # 再次检查两个点是否在同一个y-z平面上
    if x0 != x1:
        print("错误：TCP中心点和点P必须具有相同的x坐标。")
        return None
        
    # 计算向量的分量
    delta_y = y1 - y0
    delta_z = z1 - z0

    # 使用atan2计算弧度。atan2(z, y)可以处理所有象限
    angle_radians = math.atan2(delta_z, delta_y)

    # 将弧度转换为度
    angle_degrees = math.degrees(angle_radians)

    return angle_degrees

# --- 使用示例 ---

# 定义TCP中心点
tcp = (10.0, 50.0, 100.0)

# 定义在同一y-z平面内的点P
p = (10.0, 50.0, 120.0) # 这个点在TCP正上方20个单位处

# --- 新增：计算初始角度 ---
initial_angle = calculate_angle_with_horizontal(tcp, p)
if initial_angle is not None:
    print(f"向量 TCP->P 与Y轴正方向的初始夹角: {initial_angle:.4f} 度")
    print("-" * 30)

# 定义旋转角度
angle = 90.0 # 旋转90度

# 调用函数进行计算
new_p_coords = rotate_point_in_yz_plane(tcp, p, angle)

# 打印结果
if new_p_coords:
    print(f"原始TCP中心点坐标: {tcp}")
    print(f"原始点P的坐标: {p}")
    print(f"旋转角度: {angle} 度")
    print("-" * 30)
    # 将浮点数结果格式化，方便阅读
    formatted_coords = tuple(round(coord, 4) for coord in new_p_coords)
    print(f"旋转后点P'的新坐标: {formatted_coords}")

# --- 另一个示例 ---
print("\n" + "="*40 + "\n")

p2 = (10.0, 70.0, 100.0) # 这个点在TCP的Y轴正方向20个单位处
angle2 = -45.0 # 顺时针旋转45度

# --- 新增：计算初始角度 ---
initial_angle_2 = calculate_angle_with_horizontal(tcp, p2)
if initial_angle_2 is not None:
    print(f"向量 TCP->P2 与Y轴正方向的初始夹角: {initial_angle_2:.4f} 度")
    print("-" * 30)

new_p2_coords = rotate_point_in_yz_plane(tcp, p2, angle2)

if new_p2_coords:
    print(f"原始TCP中心点坐标: {tcp}")
    print(f"原始点P的坐标: {p2}")
    print(f"旋转角度: {angle2} 度")
    print("-" * 30)
    formatted_coords = tuple(round(coord, 4) for coord in new_p2_coords)
    print(f"旋转后点P'的新坐标: {formatted_coords}")

