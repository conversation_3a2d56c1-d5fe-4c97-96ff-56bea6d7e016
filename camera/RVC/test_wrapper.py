"""
Test script for RVC Camera Wrapper

This script provides basic tests to verify the functionality of the RVCCameraWrapper class.
Run this script to check if your camera setup is working correctly.

Author: Jack
Date: 2025-07-23
"""

import sys
import traceback
from rvc_camera_wrapper import RVCCameraWrapper, CameraType, CameraID, quick_capture_x2


def test_system_initialization():
    """Test basic system initialization"""
    print("Testing system initialization...")
    
    try:
        camera = RVCCameraWrapper(CameraType.X2)
        success = camera.initialize()
        camera.cleanup()
        
        if success:
            print("✓ System initialization: PASSED")
            return True
        else:
            print("✗ System initialization: FAILED")
            return False
            
    except Exception as e:
        print(f"✗ System initialization: ERROR - {e}")
        return False


def test_device_detection():
    """Test device detection and info retrieval"""
    print("Testing device detection...")
    
    try:
        with RVCCameraWrapper(CameraType.X2) as camera:
            device_info = camera.get_device_info()
            
            if device_info and 'serial_number' in device_info:
                print(f"✓ Device detection: PASSED")
                print(f"  Device: {device_info.get('name', 'Unknown')}")
                print(f"  Serial: {device_info.get('serial_number', 'Unknown')}")
                print(f"  X2 Support: {device_info.get('support_x2', False)}")
                return True
            else:
                print("✗ Device detection: FAILED - No device info")
                return False
                
    except Exception as e:
        print(f"✗ Device detection: ERROR - {e}")
        return False


def test_camera_parameters():
    """Test camera parameter retrieval"""
    print("Testing camera parameters...")
    
    try:
        with RVCCameraWrapper(CameraType.X2) as camera:
            intrinsic, distortion = camera.get_camera_parameters()
            
            if intrinsic is not None and distortion is not None:
                print("✓ Camera parameters: PASSED")
                print(f"  Intrinsic matrix shape: {intrinsic.shape}")
                print(f"  Distortion coeffs shape: {distortion.shape}")
                return True
            else:
                print("✗ Camera parameters: FAILED - No parameters retrieved")
                return False
                
    except Exception as e:
        print(f"✗ Camera parameters: ERROR - {e}")
        return False


def test_single_capture():
    """Test single frame capture"""
    print("Testing single capture...")
    
    try:
        with RVCCameraWrapper(CameraType.X2) as camera:
            # Set exposure time
            camera.set_exposure_time(50)
            
            # Capture frame
            if camera.capture_single():
                # Get image
                img = camera.get_image()
                if img is not None:
                    print(f"✓ Single capture: PASSED")
                    print(f"  Image shape: {img.shape}")
                    print(f"  Image dtype: {img.dtype}")
                    return True
                else:
                    print("✗ Single capture: FAILED - No image data")
                    return False
            else:
                print("✗ Single capture: FAILED - Capture failed")
                return False
                
    except Exception as e:
        print(f"✗ Single capture: ERROR - {e}")
        return False


def test_point_cloud_capture():
    """Test point cloud data capture"""
    print("Testing point cloud capture...")
    
    try:
        with RVCCameraWrapper(CameraType.X2) as camera:
            if camera.capture_single():
                pointmap = camera.get_point_map()
                if pointmap is not None:
                    print("✓ Point cloud capture: PASSED")
                    print(f"  Point cloud shape: {pointmap.shape}")
                    print(f"  Point cloud dtype: {pointmap.dtype}")
                    return True
                else:
                    print("✗ Point cloud capture: FAILED - No point cloud data")
                    return False
            else:
                print("✗ Point cloud capture: FAILED - Capture failed")
                return False
                
    except Exception as e:
        print(f"✗ Point cloud capture: ERROR - {e}")
        return False


def test_data_saving():
    """Test data saving functionality"""
    print("Testing data saving...")
    
    try:
        with RVCCameraWrapper(CameraType.X2) as camera:
            if camera.capture_single():
                save_dir = camera.save_data("Data/test_save", prefix="test_")
                if save_dir:
                    print("✓ Data saving: PASSED")
                    print(f"  Saved to: {save_dir}")
                    return True
                else:
                    print("✗ Data saving: FAILED - No save directory returned")
                    return False
            else:
                print("✗ Data saving: FAILED - Capture failed")
                return False
                
    except Exception as e:
        print(f"✗ Data saving: ERROR - {e}")
        return False


def test_quick_functions():
    """Test convenience functions"""
    print("Testing quick functions...")
    
    try:
        save_dir = quick_capture_x2(save_dir="Data/test_quick")
        if save_dir:
            print("✓ Quick functions: PASSED")
            print(f"  Quick capture saved to: {save_dir}")
            return True
        else:
            print("✗ Quick functions: FAILED - No save directory returned")
            return False
            
    except Exception as e:
        print(f"✗ Quick functions: ERROR - {e}")
        return False


def test_error_handling():
    """Test error handling"""
    print("Testing error handling...")
    
    try:
        # Test using camera without initialization
        camera = RVCCameraWrapper(CameraType.X2)
        
        # These should fail gracefully
        capture_result = camera.capture_single()
        image_result = camera.get_image()
        ready_status = camera.is_ready()
        
        if not capture_result and image_result is None and not ready_status:
            print("✓ Error handling: PASSED")
            print("  Graceful failure when camera not initialized")
            return True
        else:
            print("✗ Error handling: FAILED - Should have failed gracefully")
            return False
            
    except Exception as e:
        print(f"✗ Error handling: ERROR - {e}")
        return False


def run_all_tests():
    """Run all tests and provide summary"""
    print("RVC Camera Wrapper Test Suite")
    print("=" * 50)
    
    tests = [
        test_system_initialization,
        test_device_detection,
        test_camera_parameters,
        test_single_capture,
        test_point_cloud_capture,
        test_data_saving,
        test_quick_functions,
        test_error_handling
    ]
    
    results = []
    
    for test in tests:
        try:
            result = test()
            results.append(result)
            print()
        except Exception as e:
            print(f"Test failed with exception: {e}")
            traceback.print_exc()
            results.append(False)
            print()
    
    # Summary
    print("=" * 50)
    print("Test Summary:")
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Your RVC camera setup is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check your camera setup.")
        print("\nTroubleshooting tips:")
        print("1. Ensure RVC camera is connected and powered on")
        print("2. Check that PyRVC library is properly installed")
        print("3. Verify camera drivers are installed")
        print("4. Make sure you have proper permissions to access the camera")
        print("5. Try running the script as administrator/sudo if needed")
        return False


def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("RVC Camera Wrapper Test Script")
        print("Usage: python test_wrapper.py")
        print("\nThis script tests the basic functionality of the RVC camera wrapper.")
        print("Make sure your RVC camera is connected before running the tests.")
        return
    
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
