"""
RVC Camera Wrapper Usage Examples

This file demonstrates various ways to use the RVCCameraWrapper class
for different camera operations and scenarios.

Author: AI Assistant
Date: 2025-07-23
"""

import numpy as np
import cv2
from rvc_camera_wrapper import RVCCameraWrapper, CameraType, CameraID, quick_capture_x2, quick_capture_x1


def example_basic_usage():
    """Basic usage example with context manager"""
    print("=== Basic Usage Example ===")
    
    # Using context manager (recommended)
    with RVCCameraWrapper(CameraType.X2) as camera:
        # Capture a single frame
        if camera.capture_single():
            # Get image data
            img = camera.get_image()
            if img is not None:
                print(f"Captured image shape: {img.shape}")
            
            # Get point cloud data
            pointmap = camera.get_point_map()
            if pointmap is not None:
                print(f"Point cloud shape: {pointmap.shape}")
            
            # Save all data
            save_dir = camera.save_data("Data/example_basic")
            print(f"Data saved to: {save_dir}")


def example_manual_initialization():
    """Example with manual initialization and cleanup"""
    print("\n=== Manual Initialization Example ===")
    
    camera = RVCCameraWrapper(CameraType.X2, device_index=0)
    
    try:
        # Initialize camera
        if camera.initialize():
            # Set exposure time
            camera.set_exposure_time(50)
            
            # Capture and process
            if camera.capture_single():
                img = camera.get_image()
                if img is not None:
                    # Process image (example: convert to grayscale)
                    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                    print(f"Processed image shape: {gray.shape}")
                    
                    # Save processed image
                    cv2.imwrite("Data/processed_image.png", gray)
                    print("Processed image saved")
        else:
            print("Failed to initialize camera")
            
    finally:
        # Always cleanup
        camera.cleanup()


def example_specific_device():
    """Example using specific device serial number"""
    print("\n=== Specific Device Example ===")
    
    # Replace with your actual device serial number
    device_sn = "M2GM012W019"  # Example SN from the original code
    
    with RVCCameraWrapper(CameraType.X2, device_sn=device_sn) as camera:
        # Get device information
        device_info = camera.get_device_info()
        print(f"Device info: {device_info}")
        
        # Get camera parameters
        intrinsic, distortion = camera.get_camera_parameters()
        if intrinsic is not None:
            print("Intrinsic matrix:")
            print(intrinsic)
            print("Distortion coefficients:")
            print(distortion)


def example_continuous_capture():
    """Example of continuous capture with live display"""
    print("\n=== Continuous Capture Example ===")
    
    with RVCCameraWrapper(CameraType.X2) as camera:
        # Set exposure time for better image quality
        camera.set_exposure_time(100)
        
        # Start continuous capture
        # Save every 10 frames, display live view, capture max 100 frames
        frames_captured = camera.continuous_capture(
            save_interval=10,
            max_frames=100,
            display=True,
            save_dir="Data/continuous_example"
        )
        
        print(f"Captured {frames_captured} frames")


def example_x1_camera():
    """Example using X1 camera for depth imaging"""
    print("\n=== X1 Camera Example ===")
    
    with RVCCameraWrapper(CameraType.X1) as camera:
        if camera.capture_single():
            # Get image
            img = camera.get_image()
            if img is not None:
                print(f"X1 Image shape: {img.shape}")
                
                # Check if it's mono or color
                if len(img.shape) == 2 or img.shape[2] == 1:
                    print("This is a mono camera")
                else:
                    print("This is a color camera")
            
            # Get depth map
            depth_map = camera.get_depth_map()
            if depth_map is not None:
                print("Depth map captured successfully")
            
            # Save data
            save_dir = camera.save_data("Data/x1_example")
            print(f"X1 data saved to: {save_dir}")


def example_quick_functions():
    """Example using convenience functions"""
    print("\n=== Quick Functions Example ===")
    
    # Quick X2 capture
    save_dir = quick_capture_x2(save_dir="Data/quick_x2")
    if save_dir:
        print(f"Quick X2 capture saved to: {save_dir}")
    
    # Quick X1 capture
    save_dir = quick_capture_x1(save_dir="Data/quick_x1")
    if save_dir:
        print(f"Quick X1 capture saved to: {save_dir}")


def example_error_handling():
    """Example demonstrating error handling"""
    print("\n=== Error Handling Example ===")
    
    # Try to use camera without initialization
    camera = RVCCameraWrapper(CameraType.X2)
    
    # This should fail gracefully
    if not camera.capture_single():
        print("Capture failed as expected (camera not initialized)")
    
    # This should also fail gracefully
    img = camera.get_image()
    if img is None:
        print("Get image failed as expected (camera not initialized)")
    
    # Check camera readiness
    if not camera.is_ready():
        print("Camera is not ready as expected")


def example_custom_save():
    """Example with custom save options"""
    print("\n=== Custom Save Example ===")
    
    with RVCCameraWrapper(CameraType.X2) as camera:
        if camera.capture_single():
            # Save only specific data types
            save_dir = camera.save_data(
                save_dir="Data/custom_save",
                prefix="custom_",
                save_image=True,
                save_pointmap=True,
                save_depthmap=False,  # Skip depth map
                save_colored_pointmap=False  # Skip colored point map
            )
            print(f"Custom data saved to: {save_dir}")


def main():
    """Run all examples"""
    print("RVC Camera Wrapper Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_usage()
        example_manual_initialization()
        # example_specific_device()  # Uncomment if you have a specific device
        # example_continuous_capture()  # Uncomment for interactive demo
        example_x1_camera()
        example_quick_functions()
        example_error_handling()
        example_custom_save()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have:")
        print("1. RVC camera connected")
        print("2. PyRVC library installed")
        print("3. Proper permissions to access the camera")


if __name__ == "__main__":
    main()
