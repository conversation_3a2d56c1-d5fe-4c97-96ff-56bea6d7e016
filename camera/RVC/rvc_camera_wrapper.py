"""
RVC Camera Wrapper Class

A comprehensive wrapper for RVC X1/X2 cameras that simplifies initialization,
configuration, and data capture operations.

Author: Jack
Date: 2025-07-23
"""

import PyRVC as RVC
import numpy as np
import cv2
import os
import datetime
from typing import Optional, Tuple, Union, List
from enum import Enum
from Utils.Tools import TryCreateDir, generate_timestamp_filename, create_save_directory_structure


class CameraType(Enum):
    """Supported camera types"""
    X1 = "X1"
    X2 = "X2"


class CameraID(Enum):
    """Camera ID enumeration"""
    LEFT = RVC.CameraID_Left
    RIGHT = RVC.CameraID_Right


class RVCCameraWrapper:
    """
    A comprehensive wrapper class for RVC cameras that provides:
    - Simplified initialization and cleanup
    - Easy configuration management
    - Convenient data capture methods
    - Automatic error handling and resource management
    """
    
    def __init__(self, camera_type: CameraType = CameraType.X2, device_index: int = 0, 
                 device_sn: Optional[str] = None, camera_id: CameraID = CameraID.LEFT):
        """
        Initialize RVC Camera Wrapper
        
        Args:
            camera_type: Type of camera (X1 or X2)
            device_index: Device index (used if device_sn is None)
            device_sn: Device serial number (recommended for multiple cameras)
            camera_id: Camera ID (Left or Right)
        """
        self.camera_type = camera_type
        self.device_index = device_index
        self.device_sn = device_sn
        self.camera_id = camera_id
        
        # Internal state
        self._system_initialized = False
        self._camera = None
        self._device = None
        self._device_info = None
        self._is_open = False
        self._capture_options = None
        
        # Default configuration
        self.default_exposure_time = 100
        self.default_save_dir = "Data"
        
    def __enter__(self):
        """Context manager entry"""
        self.initialize()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with automatic cleanup"""
        self.cleanup()
        
    def initialize(self) -> bool:
        """
        Initialize the RVC system and camera
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Initialize RVC system
            if not self._system_initialized:
                RVC.SystemInit()
                self._system_initialized = True
                print("RVC System initialized")
            
            # Find device
            if not self._find_device():
                return False
                
            # Create camera
            if not self._create_camera():
                return False
                
            # Open camera
            if not self._open_camera():
                return False
                
            print(f"RVC {self.camera_type.value} Camera initialized successfully")
            return True
            
        except Exception as e:
            print(f"Failed to initialize camera: {e}")
            self.cleanup()
            return False
    
    def _find_device(self) -> bool:
        """Find and select the camera device"""
        try:
            if self.device_sn:
                # Find device by serial number (recommended)
                self._device = RVC.SystemFindDevice(self.device_sn)
                if not self._device:
                    print(f"Device with SN {self.device_sn} not found")
                    return False
            else:
                # Find device by index
                ret, devices = RVC.SystemListDevices(RVC.SystemListDeviceTypeEnum.All)
                if len(devices) == 0:
                    print("No RVC devices found")
                    return False
                    
                if self.device_index >= len(devices):
                    print(f"Device index {self.device_index} out of range")
                    return False
                    
                self._device = devices[self.device_index]
            
            # Get device info
            ret, self._device_info = self._device.GetDeviceInfo()
            if ret:
                print(f"Found device: {self._device_info.name}-{self._device_info.sn}")
                
                # Check camera type support
                if self.camera_type == CameraType.X2 and not self._device_info.support_x2:
                    print("Device does not support X2 function")
                    return False
                    
                return True
            else:
                print("Failed to get device info")
                return False
                
        except Exception as e:
            print(f"Error finding device: {e}")
            return False
    
    def _create_camera(self) -> bool:
        """Create camera instance"""
        try:
            if self.camera_type == CameraType.X1:
                self._camera = RVC.X1.Create(self._device, self.camera_id.value)
            else:  # X2
                self._camera = RVC.X2.Create(self._device)
                
            if not self._camera or not self._camera.IsValid():
                print("Failed to create valid camera")
                return False
                
            print("Camera created successfully")
            return True
            
        except Exception as e:
            print(f"Error creating camera: {e}")
            return False
    
    def _open_camera(self) -> bool:
        """Open camera connection"""
        try:
            ret = self._camera.Open()
            if ret and self._camera.IsOpen():
                self._is_open = True
                
                # Load default capture options for X2
                if self.camera_type == CameraType.X2:
                    _, self._capture_options = self._camera.LoadCaptureOptionParameters()
                    self._capture_options.exposure_time_2d = self.default_exposure_time
                
                # Print exposure time range
                _, exp_min, exp_max = self._camera.GetExposureTimeRange()
                print(f"Exposure time range: [{exp_min}, {exp_max}]")
                
                print("Camera opened successfully")
                return True
            else:
                print("Failed to open camera")
                return False
                
        except Exception as e:
            print(f"Error opening camera: {e}")
            return False

    def cleanup(self):
        """Clean up resources and shutdown system"""
        try:
            if self._camera and self._is_open:
                self._camera.Close()
                self._is_open = False
                print("Camera closed")

            if self._camera:
                if self.camera_type == CameraType.X1:
                    RVC.X1.Destroy(self._camera)
                else:
                    RVC.X2.Destroy(self._camera)
                self._camera = None
                print("Camera destroyed")

            if self._system_initialized:
                RVC.SystemShutdown()
                self._system_initialized = False
                print("RVC System shutdown")

        except Exception as e:
            print(f"Error during cleanup: {e}")

    def is_ready(self) -> bool:
        """Check if camera is ready for operations"""
        return (self._system_initialized and
                self._camera is not None and
                self._is_open and
                self._camera.IsValid())

    def set_exposure_time(self, exposure_time: int) -> bool:
        """
        Set camera exposure time

        Args:
            exposure_time: Exposure time value

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_ready():
            print("Camera not ready")
            return False

        try:
            if self.camera_type == CameraType.X2 and self._capture_options:
                self._capture_options.exposure_time_2d = exposure_time
                print(f"Exposure time set to {exposure_time}")
                return True
            else:
                print("Exposure time setting not supported for this camera type")
                return False

        except Exception as e:
            print(f"Error setting exposure time: {e}")
            return False

    def capture_single(self) -> bool:
        """
        Capture a single frame

        Returns:
            bool: True if capture successful, False otherwise
        """
        if not self.is_ready():
            print("Camera not ready")
            return False

        try:
            if self.camera_type == CameraType.X2 and self._capture_options:
                ret = self._camera.Capture(self._capture_options)
            else:
                ret = self._camera.Capture()

            if not ret:
                print("Capture failed")
                print(RVC.GetLastErrorMessage())
                return False

            return True

        except Exception as e:
            print(f"Error during capture: {e}")
            return False

    def get_image(self, camera_id: Optional[CameraID] = None) -> Optional[np.ndarray]:
        """
        Get captured image as numpy array

        Args:
            camera_id: Camera ID (for X2 cameras), uses default if None

        Returns:
            np.ndarray: Image array or None if failed
        """
        if not self.is_ready():
            print("Camera not ready")
            return None

        try:
            if self.camera_type == CameraType.X2:
                cam_id = camera_id.value if camera_id else self.camera_id.value
                img = self._camera.GetImage(cam_id)
            else:
                img = self._camera.GetImage()

            if img:
                return np.array(img, copy=False)
            else:
                print("Failed to get image")
                return None

        except Exception as e:
            print(f"Error getting image: {e}")
            return None

    def get_point_map(self) -> Optional[np.ndarray]:
        """
        Get captured point map as numpy array

        Returns:
            np.ndarray: Point map array (Nx3) or None if failed
        """
        if not self.is_ready():
            print("Camera not ready")
            return None

        try:
            pm = self._camera.GetPointMap()
            if pm:
                return np.array(pm, copy=False).reshape(-1, 3)
            else:
                print("Failed to get point map")
                return None

        except Exception as e:
            print(f"Error getting point map: {e}")
            return None

    def get_depth_map(self):
        """
        Get captured depth map

        Returns:
            Depth map object or None if failed
        """
        if not self.is_ready():
            print("Camera not ready")
            return None

        try:
            dp = self._camera.GetDepthMap()
            if dp:
                return dp
            else:
                print("Failed to get depth map")
                return None

        except Exception as e:
            print(f"Error getting depth map: {e}")
            return None

    def get_camera_parameters(self) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Get camera intrinsic parameters

        Returns:
            Tuple[np.ndarray, np.ndarray]: (intrinsic_matrix, distortion_coeffs) or (None, None)
        """
        if not self.is_ready():
            print("Camera not ready")
            return None, None

        try:
            if self.camera_type == CameraType.X2:
                ret, intrinsic, distortion = self._camera.GetIntrinsicParameters(self.camera_id.value)
            else:
                ret, intrinsic, distortion = self._camera.GetIntrinsicParameters()

            if ret:
                return intrinsic, distortion
            else:
                print("Failed to get camera parameters")
                return None, None

        except Exception as e:
            print(f"Error getting camera parameters: {e}")
            return None, None

    def save_data(self, save_dir: Optional[str] = None, prefix: str = "",
                  save_image: bool = True, save_pointmap: bool = True,
                  save_depthmap: bool = True, save_colored_pointmap: bool = True) -> str:
        """
        Save captured data to files

        Args:
            save_dir: Directory to save files (uses default if None)
            prefix: Prefix for filenames
            save_image: Whether to save image
            save_pointmap: Whether to save point map
            save_depthmap: Whether to save depth map
            save_colored_pointmap: Whether to save colored point map

        Returns:
            str: Directory path where files were saved
        """
        if not self.is_ready():
            print("Camera not ready")
            return ""

        # Create save directory
        if save_dir is None:
            save_dir = self.default_save_dir

        device_sn = self._device_info.sn if self._device_info else "unknown"
        camera_type_dir = self.camera_type.value.lower()
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

        final_dir = create_save_directory_structure(
            save_dir, device_sn, [camera_type_dir, timestamp]
        )

        try:
            # Get data
            if save_image:
                img = self._camera.GetImage(self.camera_id.value) if self.camera_type == CameraType.X2 else self._camera.GetImage()
                if img:
                    filename = os.path.join(final_dir, f"{prefix}image.png" if prefix else "image.png")
                    img.SaveImage(filename)
                    print(f"Image saved: {filename}")

            if save_pointmap or save_colored_pointmap:
                pm = self._camera.GetPointMap()
                if pm:
                    if save_pointmap:
                        filename = os.path.join(final_dir, f"{prefix}pointmap.ply" if prefix else "pointmap.ply")
                        pm.Save(filename, RVC.PointMapUnitEnum.Meter, True)
                        print(f"Point map saved: {filename}")

                    if save_colored_pointmap and save_image:
                        img = self._camera.GetImage(self.camera_id.value) if self.camera_type == CameraType.X2 else self._camera.GetImage()
                        if img:
                            filename = os.path.join(final_dir, f"{prefix}pointmap_color.ply" if prefix else "pointmap_color.ply")
                            pm.SaveWithImage(filename, img, RVC.PointMapUnitEnum.Meter, True)
                            print(f"Colored point map saved: {filename}")

            if save_depthmap:
                dp = self._camera.GetDepthMap()
                if dp:
                    filename = os.path.join(final_dir, f"{prefix}depthmap.tiff" if prefix else "depthmap.tiff")
                    dp.SaveDepthMap(filename, True)
                    print(f"Depth map saved: {filename}")

            return final_dir

        except Exception as e:
            print(f"Error saving data: {e}")
            return ""

    def continuous_capture(self, save_interval: int = 10, max_frames: int = 0,
                          display: bool = True, save_dir: Optional[str] = None) -> int:
        """
        Continuous capture with real-time display and auto-save

        Args:
            save_interval: Save every N frames (0 = no auto-save)
            max_frames: Maximum frames to capture (0 = unlimited)
            display: Whether to display images in real-time
            save_dir: Directory to save files

        Returns:
            int: Number of frames captured
        """
        if not self.is_ready():
            print("Camera not ready")
            return 0

        if self.camera_type != CameraType.X2:
            print("Continuous capture only supported for X2 cameras")
            return 0

        # Setup save directory if needed
        if save_interval > 0:
            if save_dir is None:
                save_dir = self.default_save_dir
            device_sn = self._device_info.sn if self._device_info else "unknown"
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            final_dir = create_save_directory_structure(
                save_dir, device_sn, ["continuous", timestamp]
            )

        # Setup display window
        window_name = "RVC Camera Live View"
        if display:
            cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)

        print(f"Starting continuous capture...")
        print(f"Press ESC to exit")
        if save_interval > 0:
            print(f"Auto-saving every {save_interval} frames...")

        frame_count = 0
        saved_count = 0

        try:
            while True:
                # Check frame limit
                if max_frames > 0 and frame_count >= max_frames:
                    print(f"Reached maximum frames: {max_frames}")
                    break

                # Capture frame
                if not self.capture_single():
                    print("Capture failed, continuing...")
                    continue

                frame_count += 1

                # Get and display image
                if display:
                    img_np = self.get_image()
                    if img_np is not None:
                        cv2.imshow(window_name, img_np)

                # Auto-save
                if save_interval > 0 and frame_count % save_interval == 0:
                    saved_count += 1
                    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                    prefix = f"frame_{saved_count:04d}_{timestamp}_"
                    self.save_data(final_dir, prefix, save_pointmap=False,
                                 save_depthmap=False, save_colored_pointmap=False)
                    print(f"Frame {frame_count} saved")

                # Handle keyboard input
                if display:
                    key = cv2.waitKey(1) & 0xFF
                    if key == 27:  # ESC key
                        print("Exit requested")
                        break

                    # Check if window was closed
                    if cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE) < 1:
                        break

        except KeyboardInterrupt:
            print("Interrupted by user")
        except Exception as e:
            print(f"Error during continuous capture: {e}")
        finally:
            if display:
                cv2.destroyAllWindows()

        print(f"Capture completed. Total frames: {frame_count}")
        return frame_count

    def get_device_info(self) -> dict:
        """
        Get device information

        Returns:
            dict: Device information
        """
        if self._device_info:
            return {
                'name': self._device_info.name,
                'serial_number': self._device_info.sn,
                'support_x2': self._device_info.support_x2,
                'camera_type': self.camera_type.value,
                'camera_id': self.camera_id.name
            }
        else:
            return {}


# Convenience functions for quick usage
def quick_capture_x2(device_sn: Optional[str] = None, save_dir: str = "Data") -> str:
    """
    Quick single capture with X2 camera

    Args:
        device_sn: Device serial number (None for first available)
        save_dir: Directory to save data

    Returns:
        str: Directory where data was saved
    """
    with RVCCameraWrapper(CameraType.X2, device_sn=device_sn) as camera:
        if camera.capture_single():
            return camera.save_data(save_dir)
        else:
            return ""


def quick_capture_x1(device_sn: Optional[str] = None, save_dir: str = "Data") -> str:
    """
    Quick single capture with X1 camera

    Args:
        device_sn: Device serial number (None for first available)
        save_dir: Directory to save data

    Returns:
        str: Directory where data was saved
    """
    with RVCCameraWrapper(CameraType.X1, device_sn=device_sn) as camera:
        if camera.capture_single():
            return camera.save_data(save_dir)
        else:
            return ""
