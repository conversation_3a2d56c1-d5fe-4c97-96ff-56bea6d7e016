# Copyright (c) RVBUST, Inc - All rights reserved.
import PyRVC as RVC
import os
import numpy as np
import cv2
import time
import datetime
from Utils.Tools import *


def ContinuousCapture(save_interval=10):
    """Simple continuous capture with real-time display and auto-save
    
    Args:
        save_interval (int): Save every N frames (default: 10)
    """
    
    # Initialize RVC X system
    RVC.SystemInit()

    # Find Device by index
    ret, devices = RVC.SystemListDevices(RVC.SystemListDeviceTypeEnum.All)
    if len(devices) == 0:
        print("Can not find any Device!")
        RVC.SystemShutdown()
        return -1
    device = devices[0]

    # Get device info
    ret, info = device.GetDeviceInfo()
    print(info.name + "-" + info.sn)

    if not info.support_x2:
        print("Device does not support x2 function!")
        RVC.SystemShutdown()
        return -1

    # Create and Open X2
    x2 = RVC.X2.Create(device)
    ret = x2.Open()
    if ret and x2.IsValid():
        print("Camera is valid!")
    else:
        print("Camera is not valid!")
        RVC.X2.Destroy(x2)
        RVC.SystemShutdown()
        return -1

    # Print ExposureTime Range
    _, exp_range_min, exp_range_max = x2.GetExposureTimeRange()
    print("ExposureTime Range:[{}, {}]".format(exp_range_min, exp_range_max))
    _, options = x2.LoadCaptureOptionParameters()
    options.exposure_time_2d = 100

    # Create save directory
    save_address = "Data"
    TryCreateDir(save_address)
    save_address += "/" + info.sn
    TryCreateDir(save_address)
    save_address += "/continuous"
    TryCreateDir(save_address)
    save_address += "/kakou_0722"
    TryCreateDir(save_address)

    # Create display window
    window_name = "RVC X2 Live View"
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    
    print(f"\nPress ESC to exit")
    print(f"Auto-saving every {save_interval} frames...")
    
    frame_count = 0
    saved_count = 235
    
    # Main capture loop
    while True:
        # Capture frame
        ret = x2.Capture(options)
        if not ret:
            print("Capture failed!")
            continue
            
        # Get left camera image
        img = x2.GetImage(RVC.CameraID_Left)
        img_np = np.array(img, copy=False)
        
        # Increment frame counter
        frame_count += 1
        
        # Auto-save with frame skipping
        if frame_count % save_interval == 0:
            saved_count += 1
            filename = f"{save_address}/frame_{saved_count:04d}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            img.SaveImage(filename)
            print(f"Frame {frame_count} saved: {filename}")
        
        # Display image
        cv2.imshow(window_name, img_np)
        
        # Handle keyboard input
        key = cv2.waitKey(1) & 0xFF
        
        if key == 27:  # ESC key
            print("Exit requested")
            break
            
        # Check if window was closed
        if cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE) < 1:
            break

    # Cleanup
    cv2.destroyAllWindows()
    x2.Close()
    RVC.X2.Destroy(x2)
    RVC.SystemShutdown()
    
    print("Capture completed")
    return 0


if __name__ == "__main__":
    # You can change the save_interval parameter to adjust frame skipping
    # Default: save every 10 frames
    ContinuousCapture(save_interval=1)
    