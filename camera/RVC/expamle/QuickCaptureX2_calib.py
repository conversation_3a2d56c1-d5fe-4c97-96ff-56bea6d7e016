# Copyright (c) RVBUST, Inc - All rights reserved.
import PyRVC as RVC
import os
import numpy as np
import cv2
from Utils.Tools import *


def App(index=0):
    # Initialize RVC X system.
    RVC.SystemInit()

    # Find Device

    # Method 1,Find device by index.
    ret, devices = RVC.SystemListDevices(RVC.SystemListDeviceTypeEnum.All)
    if len(devices) == 0:
        print("Can not find any Device!")
        RVC.SystemShutdown()
        return -1
    device = devices[0]

    # Method 2,Find device by sn.
    # This is the most recommended method when you have multiple cameras.
    # device = RVC.SystemFindDevice("M2GM012W019")

    ret, info = device.GetDeviceInfo()
    print(info.name + "-" + info.sn)

    if not info.support_x2:
        print("Device does not support x2 function !")
        RVC.SystemShutdown()
        return -1

    # Create and Open
    x2 = RVC.X2.Create(device)
    ret = x2.Open()
    if ret and x2.IsValid():
        print("Camera is valid!")
    else:
        print("Camera is not valid!")
        RVC.X2.Destroy(x2)
        RVC.SystemShutdown()
        return -1

    # PrintCaptureMode(devices[0])
    # Print ExposureTime Range
    _, exp_range_min, exp_range_max = x2.GetExposureTimeRange()
    print("ExposureTime Range:[{}, {}]".format(exp_range_min, exp_range_max))
    _, options = x2.LoadCaptureOptionParameters()
    options.exposure_time_2d = 40

    # Capture
    ret = x2.Capture(options)
    if ret:
        print("Capture successed!")
    else:
        print("Capture failed!")
        print(RVC.GetLastErrorMessage())
        x2.Close()
        RVC.X2.Destroy(x2)
        RVC.SystemShutdown()
        return -1

    # Data Process
    save_address = "Data"
    TryCreateDir(save_address)
    save_address += "/" + info.sn
    TryCreateDir(save_address)
    save_address += "/x2"
    TryCreateDir(save_address)
    save_address += "/calibrate"
    TryCreateDir(save_address)

    img = x2.GetImage(RVC.CameraID_Left)
    pm = x2.GetPointMap()

    # pm.Save(save_address + f"/pointmap_{index}.ply",
    #         RVC.PointMapUnitEnum.Millimeter, True)
    pm.SaveWithImage(save_address + f"/pointmap_color_{index}.ply",
                     img, RVC.PointMapUnitEnum.Millimeter, True)
    img.SaveImage(save_address + f"/image_{index}.png")

    # numpy extension
    img_np = np.array(img, copy=False)
    pm_np = np.array(pm, copy=False).reshape(-1, 3)
    size = img_np.size

    # Release
    x2.Close()
    RVC.X2.Destroy(x2)
    RVC.SystemShutdown()

    import rtde_control
    import rtde_receive

    # --- Begin of left ---
    
   # ROBOT_HOST = "*************" # UR - Right 
    ROBOT_HOST = "*************" # UR - left 

    rtde_c_left = rtde_control.RTDEControlInterface(ROBOT_HOST)
    rtde_r_left = rtde_receive.RTDEReceiveInterface(ROBOT_HOST)

    tcp_pose = rtde_r_left.getActualTCPPose()
    print("--- LEFT TCP Pose ---", tcp_pose)


    return 0


if __name__ == "__main__":
    App(index=15)
