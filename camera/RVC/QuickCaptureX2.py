# Copyright (c) RVBUST, Inc - All rights reserved.
import PyRVC as RVC
import os
import numpy as np
import cv2
from Utils.Tools import *

def App():
    # Initialize RVC X system.
    RVC.SystemInit()

    # Find Device

    # Method 1,Find device by index.
    ret, devices = RVC.SystemListDevices(RVC.SystemListDeviceTypeEnum.All)
    if len(devices) == 0:
        print("Can not find any Device!")
        RVC.SystemShutdown()
        return -1
    device = devices[0]

    # Method 2,Find device by sn.
    # This is the most recommended method when you have multiple cameras.
    # device = RVC.SystemFindDevice("M2GM012W019")

    ret, info = device.GetDeviceInfo()
    print(info.name + "-" + info.sn)

    if not info.support_x2:
        print("Device does not support x2 function !")
        RVC.SystemShutdown()
        return -1

    # Create and Open
    x2 = RVC.X2.Create(device)
    ret = x2.Open()
    if ret and x2.IsValid():
        print("Camera is valid!")
    else:
        print("Camera is not valid!")
        RVC.X2.Destroy(x2)
        RVC.SystemShutdown()
        return -1

    # PrintCaptureMode(devices[0])
    # Print ExposureTime Range
    _, exp_range_min, exp_range_max = x2.GetExposureTimeRange()
    print("ExposureTime Range:[{}, {}]".format(exp_range_min, exp_range_max))

    # Capture
    ret = x2.Capture()
    if ret:
        print("Capture successed!")
    else:
        print("Capture failed!")
        print(RVC.GetLastErrorMessage())
        x2.Close()
        RVC.X2.Destroy(x2)
        RVC.SystemShutdown()
        return -1

    # Data Process
    save_address = "Data"
    TryCreateDir(save_address)
    save_address += "/" + info.sn
    TryCreateDir(save_address)
    save_address += "/x2"
    TryCreateDir(save_address)
    save_address += "/data_2"
    TryCreateDir(save_address)

    img = x2.GetImage(RVC.CameraID_Left)
    pm = x2.GetPointMap()
    # depth map in meters
    dp = x2.GetDepthMap() 

    pm.Save(save_address + "/pointmap.ply",
            RVC.PointMapUnitEnum.Meter, True)
    pm.SaveWithImage(save_address + "/pointmap_color.ply",
                     img, RVC.PointMapUnitEnum.Meter, True)
    img.SaveImage(save_address + "/image.png")
    dp.SaveDepthMap(save_address + "/depthmap.tiff", True)

    # numpy extension
    img_np = np.array(img, copy=False)
    pm_np = np.array(pm, copy=False).reshape(-1, 3)
    size = img_np.size

    # if True:
    #     ret, T_extrinsic = x2.GetExtrinsicMatrix(RVC.CameraID_Left)
    #     if ret == True:
    #         print("\nExtrinsicMatrix:")
    #         print(T_extrinsic)

    #     ret, T_intrinsic_matrix, T_distortion = x2.GetIntrinsicParameters(RVC.CameraID_Left)
    #     print("Done!")
    #     if ret == True:
    #         print("\nIntrinsicParameters:")
    #         print(T_intrinsic_matrix)
    #         print("\ndistortion:")
    #         print(T_distortion)

    # Release
    x2.Close()
    RVC.X2.Destroy(x2)
    RVC.SystemShutdown()

    return 0


if __name__ == "__main__":
    App()
