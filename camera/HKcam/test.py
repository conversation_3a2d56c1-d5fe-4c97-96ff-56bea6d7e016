import time

import cv2
from pyhkcam import information
from pyhkcam.pyhkcam import *
device_info = enumerateDevice()
print(device_info)

# hkcam = HKCam(acSn='DSU004600000383')
hkcam = HKCam(index=0)
# hkcam.setTriggerSource(information.TriggerSource.SOFTWARE)
# hkcam.setTriggerCount(1)
hkcam.start()
# time.sleep(0.2)
while True:
    # hkcam.softTrigger()
    cv2.namedWindow('img', cv2.WINDOW_NORMAL)
    img = hkcam.readImage(timeout_ms=1000)
    if img is not None:
        cv2.imshow('img', img)
        cv2.waitKey(30)
    else:
        print('none')
