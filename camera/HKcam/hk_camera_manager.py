import time
import cv2
from pyhkcam import information
from pyhkcam.pyhkcam import *


class HKCameraManager:
    """
    HK Camera Manager - Encapsulates HiKvision camera functionality
    Based on the tested functionality in test.py
    """
    
    def __init__(self, index=0, serial_number=None):
        """
        Initialize HK Camera Manager
        
        Args:
            index (int): Camera index (default: 0)
            serial_number (str): Camera serial number (optional, e.g., 'DSU004600000383')
        """
        self.index = index
        self.serial_number = serial_number
        self.hkcam = None
        self.is_started = False
        
    def enumerate_devices(self):
        """
        Enumerate available HK camera devices
        
        Returns:
            device_info: Information about available devices
        """
        try:
            device_info = enumerateDevice()
            print(f"Available devices: {device_info}")
            return device_info
        except Exception as e:
            print(f"Error enumerating devices: {e}")
            return None
    
    def connect(self):
        """
        Connect to the HK camera
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Enumerate devices first
            self.enumerate_devices()
            
            # Initialize camera based on provided parameters
            if self.serial_number:
                self.hkcam = HKCam(acSn=self.serial_number)
                print(f"Connected to camera with serial number: {self.serial_number}")
            else:
                self.hkcam = HKCam(index=self.index)
                print(f"Connected to camera with index: {self.index}")
            
            return True
            
        except Exception as e:
            print(f"Error connecting to camera: {e}")
            return False
    
    def start_capture(self):
        """
        Start camera capture
        
        Returns:
            bool: True if start successful, False otherwise
        """
        if not self.hkcam:
            print("Camera not connected. Please call connect() first.")
            return False
        
        try:
            self.hkcam.start()
            self.is_started = True
            print("Camera capture started successfully")
            return True
            
        except Exception as e:
            print(f"Error starting camera capture: {e}")
            return False
    
    def read_image(self, timeout_ms=1000):
        """
        Read image from camera
        
        Args:
            timeout_ms (int): Timeout in milliseconds (default: 1000)
            
        Returns:
            numpy.ndarray or None: Image data if successful, None otherwise
        """
        if not self.hkcam or not self.is_started:
            print("Camera not started. Please call start_capture() first.")
            return None
        
        try:
            img = self.hkcam.readImage(timeout_ms=timeout_ms)
            return img
            
        except Exception as e:
            print(f"Error reading image: {e}")
            return None
    
    def capture_and_display(self, window_name='HK Camera', wait_key=30):
        """
        Capture and display images in real-time (blocking loop)
        Based on the functionality in test.py
        
        Args:
            window_name (str): OpenCV window name
            wait_key (int): Wait key delay in milliseconds
        """
        if not self.hkcam or not self.is_started:
            print("Camera not started. Please call start_capture() first.")
            return
        
        print("Starting live display. Press 'q' to quit.")
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        
        try:
            while True:
                img = self.read_image()
                
                if img is not None:
                    cv2.imshow(window_name, img)
                    key = cv2.waitKey(wait_key) & 0xFF
                    if key == ord('q'):
                        break
                else:
                    print('No image received')
                    
        except KeyboardInterrupt:
            print("Display interrupted by user")
        finally:
            cv2.destroyAllWindows()
    
    def capture_single_image(self, save_path=None, timeout_ms=1000):
        """
        Capture a single image
        
        Args:
            save_path (str): Path to save image (optional)
            timeout_ms (int): Timeout in milliseconds
            
        Returns:
            numpy.ndarray or None: Captured image
        """
        img = self.read_image(timeout_ms=timeout_ms)
        
        if img is not None:
            if save_path:
                try:
                    cv2.imwrite(save_path, img)
                    print(f"Image saved to: {save_path}")
                except Exception as e:
                    print(f"Error saving image: {e}")
            
            return img
        else:
            print("Failed to capture image")
            return None
    
    def set_trigger_mode(self, trigger_source=None, trigger_count=1):
        """
        Set camera trigger mode (commented out in original test.py)
        This method is provided for future use but not tested
        
        Args:
            trigger_source: Trigger source (e.g., information.TriggerSource.SOFTWARE)
            trigger_count (int): Number of triggers
        """
        if not self.hkcam:
            print("Camera not connected")
            return False
        
        try:
            if trigger_source:
                self.hkcam.setTriggerSource(trigger_source)
                print(f"Trigger source set")
            
            self.hkcam.setTriggerCount(trigger_count)
            print(f"Trigger count set to: {trigger_count}")
            return True
            
        except Exception as e:
            print(f"Error setting trigger mode: {e}")
            return False
    
    def soft_trigger(self):
        """
        Execute software trigger (commented out in original test.py)
        This method is provided for future use but not tested
        """
        if not self.hkcam:
            print("Camera not connected")
            return False
        
        try:
            self.hkcam.softTrigger()
            return True
        except Exception as e:
            print(f"Error executing soft trigger: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect and cleanup camera resources
        """
        try:
            if self.hkcam and self.is_started:
                # Note: The original pyhkcam library might not have explicit stop/disconnect methods
                # This is a placeholder for proper cleanup
                self.is_started = False
                print("Camera disconnected")
            
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"Error during disconnect: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        self.start_capture()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


# Example usage based on test.py functionality
if __name__ == "__main__":
    # Example 1: Basic usage similar to test.py
    camera = HKCameraManager(index=0)
    
    if camera.connect():
        if camera.start_capture():
            # Live display (same as test.py while loop)
            camera.capture_and_display()
    
    camera.disconnect()
    
    # Example 2: Context manager usage
    # with HKCameraManager(index=0) as camera:
    #     camera.capture_and_display()
    
    # Example 3: Single image capture
    # camera = HKCameraManager(index=0)
    # if camera.connect() and camera.start_capture():
    #     img = camera.capture_single_image("captured_image.png")
    #     if img is not None:
    #         print(f"Image captured with shape: {img.shape}")
    # camera.disconnect() 