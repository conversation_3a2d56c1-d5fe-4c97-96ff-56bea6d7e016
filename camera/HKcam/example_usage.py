"""
Example usage of HKCameraManager
This file demonstrates how to use the HKCameraManager class
which encapsulates the functionality from test.py
"""

from hk_camera_manager import HKCameraManager
import cv2
import time


def example_basic_usage():
    """
    Example 1: Basic usage similar to the original test.py
    """
    print("=== Example 1: Basic Usage ===")
    
    # Create camera manager instance
    camera = HKCameraManager(index=0)
    
    # Connect and start capture
    if camera.connect():
        if camera.start_capture():
            print("Camera ready. Starting live display...")
            # This will show live camera feed (same as test.py while loop)
            camera.capture_and_display()
        else:
            print("Failed to start camera capture")
    else:
        print("Failed to connect to camera")
    
    # Cleanup
    camera.disconnect()


def example_context_manager():
    """
    Example 2: Using context manager for automatic resource management
    """
    print("=== Example 2: Context Manager Usage ===")
    
    try:
        with HKCameraManager(index=0) as camera:
            print("Camera connected and started via context manager")
            
            # Capture a few images
            for i in range(300):
                img = camera.capture_single_image(f"data/captured_image_{i}.png")
                if img is not None:
                    print(f"Captured image {i} with shape: {img.shape}")
                time.sleep(1)
                
    except Exception as e:
        print(f"Error in context manager usage: {e}")


def example_single_image_capture():
    """
    Example 3: Single image capture and save
    """
    print("=== Example 3: Single Image Capture ===")
    
    camera = HKCameraManager(index=0)
    
    if camera.connect() and camera.start_capture():
        # Capture single image
        img = camera.capture_single_image("single_capture.png")
        
        if img is not None:
            print(f"Image captured successfully with shape: {img.shape}")
            
            # Display the captured image
            cv2.namedWindow('Captured Image', cv2.WINDOW_NORMAL)
            cv2.imshow('Captured Image', img)
            print("Press any key to close the image window...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        else:
            print("Failed to capture image")
    
    camera.disconnect()


def example_serial_number_connection():
    """
    Example 4: Connect using serial number (as shown in test.py comments)
    """
    print("=== Example 4: Serial Number Connection ===")
    
    # Using serial number from test.py comments
    camera = HKCameraManager(serial_number='DSU004600000383')
    
    if camera.connect():
        print("Connected using serial number")
        if camera.start_capture():
            # Capture one image to verify
            img = camera.capture_single_image("serial_test.png")
            if img is not None:
                print("Serial number connection successful")
        camera.disconnect()
    else:
        print("Failed to connect with serial number - falling back to index")
        # Fallback to index-based connection
        camera = HKCameraManager(index=0)
        if camera.connect() and camera.start_capture():
            print("Fallback connection successful")
            camera.disconnect()


def example_custom_display_settings():
    """
    Example 5: Custom display settings
    """
    print("=== Example 5: Custom Display Settings ===")
    
    camera = HKCameraManager(index=0)
    
    if camera.connect() and camera.start_capture():
        print("Starting custom display (press 'q' to quit)...")
        # Custom window name and faster refresh rate
        camera.capture_and_display(window_name='Custom HK Camera View', wait_key=10)
    
    camera.disconnect()


def main():
    """
    Main function to run examples
    """
    print("HKCameraManager Examples")
    print("Choose an example to run:")
    print("1. Basic usage (live display)")
    print("2. Context manager usage")
    print("3. Single image capture")
    print("4. Serial number connection")
    print("5. Custom display settings")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (0-5): ").strip()
            
            if choice == '0':
                print("Exiting...")
                break
            elif choice == '1':
                example_basic_usage()
            elif choice == '2':
                example_context_manager()
            elif choice == '3':
                example_single_image_capture()
            elif choice == '4':
                example_serial_number_connection()
            elif choice == '5':
                example_custom_display_settings()
            else:
                print("Invalid choice. Please enter 0-5.")
                
        except KeyboardInterrupt:
            print("\nProgram interrupted by user")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    # main() 
    example_context_manager()