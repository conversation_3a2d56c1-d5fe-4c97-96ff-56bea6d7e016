import cv2
import numpy as np
import matplotlib.pyplot as plt
import os

from robo_ctl.ur_robot_controller import URRobotController
from utils import pose_6d_to_matrix, matrix_to_axis_6d
from template_matcher.line_detector import (
    detect_lines, 
    calculate_rotated_vector_endpoint, 
    calculate_robot_rx_from_pixel_rotation,
    get_angle,
    calculate_angle_with_horizontal,
    rotate_point_in_yz_plane
)
from template_matcher.utils_matcher import template_matching_registration


def preprocess_image(image, is_template=False):
    """Image preprocessing: grayscale + Gaussian blur + adaptive threshold"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    thresh = cv2.adaptiveThreshold(
        blurred, 255, 
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY_INV,  21, 10 # 15, 0 # 21, 10  21, 2 # 
    )
    
    if is_template:
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    return thresh


def main():
    scene_path = "data/2d_images/0710_T4/Image_20250721143505006.bmp"
    template_path_large = "data/2d_images/template/temp_large.jpg"
    template_path_small = "data/2d_images/template/temp_small.jpg"
    template_path_rot = "data/2d_images/template/temp_rot.png"

    ROT = False
    if ROT:
        result_large_1, roi_gray_large = template_matching_registration(template_path=template_path_large, image_path=scene_path, flag="large")
    else:
        result_large_1, roi_gray_large = template_matching_registration(template_path=template_path_rot, image_path=scene_path, flag="large")    
    result_small_2, _ = template_matching_registration(template_path=template_path_small, image_path=scene_path, flag="small")

    x1, y1 = result_large_1[0]
    x2, y2 = result_small_2[0]
    print(f"Circle 1: ({x1}, {y1})")
    print(f"Circle 2: ({x2}, {y2})")

    # dx_pixel = x2 - x1
    # dy_pixel = y2 - y1
    # print(f"Pixel coordinate: dx = {dx_pixel}, dy = {dy_pixel}")

    pt1_pixel = np.array([x1, y1, 1])
    pt2_pixel = np.array([x2, y2, 1])

    R_cam_to_world = np.array([
        [-3.05701394e-06, 6.36506259e-05, -3.98354809e-01],
        [-6.31956155e-05, -4.21291944e-06, 2.21344384e-01],
        [0, 0, 1]
    ])

    R_world_to_cam = np.array([
        [ -1044.01138588, -15773.37975983,   3075.46206763],
        [ 15660.62279347,   -757.56429944,   6406.1670116 ],
        [0, 0, 1]
    ])

    # R_cam_to_world = np.array([
    #     [ 1.74529789e-05, -9.68114669e-06,  6.66878279e+02],
    #     [-4.25076605e-03,  6.57644182e-02,  8.05730062e+02],
    #     [-6.51356975e-02, -4.38722264e-03,  1.00000000e+00]
    # ])

    pt1_world = R_cam_to_world @ pt1_pixel # (y,z,1)
    print("pt1_world:", pt1_world)
    
    pt2_world = R_cam_to_world @ pt2_pixel
    d_world = (pt2_world - pt1_world)
    # print(f"world coordinate: dx = {d_world[0]}, dy = {d_world[1]}, dz = {d_world[2]}")
    print(f"world coordinate: dx = 0, dy = {d_world[0]}, dz = {d_world[1]}") 

    # === convert to  uv coordinate calculation the rotation angle ===
    # x_fixed = 0.76383
    # # cur_xyz = np.array([-0.24167, 0.36874, 1])
    # cur_xyz = np.array([-0.24167+d_world[0], 0.36874+d_world[1], 1]) # (y,z,1)
    # cur_uv_hom = R_world_to_cam @ cur_xyz # (u, v, 1)
    # # cur_uv_hom =  cur_xyz @ R_world_to_cam
    # cur_uv = cur_uv_hom[:2]  # u, v

    # angle_rad, angle_deg = get_angle(int(cur_uv[0]), int(cur_uv[1]), int(x1), int(y1))
    # print(f"angle: {angle_deg:.2f} degrees")
    # rot_uv_angle = 28.44 - angle_deg
    # print(f"rot_uv_angle: {rot_uv_angle:.2f} degrees")
    # robot_rx_deg, robot_rx_rad, v_trans_base = calculate_robot_rx_from_pixel_rotation(
    # p_target_pix=cur_uv, p_center_pix=(x2, y2), angle_a_deg=rot_uv_angle, M=R_cam_to_world) 
    # print(f"robot_rx: {robot_rx_deg:.2f} degrees, {robot_rx_rad:.2f} rad, v_trans_base: {v_trans_base}")
    
    
    if ROT:
        tcp_xyz = np.array([0.76383, -0.24167, 0.36874]) # x, y, z
        large_circle_center_xyz = np.array([0.76383, pt1_world[0], pt1_world[1]])
        print(f"large_circle_center_xyz:{large_circle_center_xyz}")
        initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_circle_center_xyz)
        print(f"向量 TCP->P 与Y轴正方向的初始夹角: {initial_angle:.4f} 度")
        print("-" * 30)
        # target_angle = calculate_angle_with_horizontal(tcp_xyz, large_circle_center_xyz)
        rot_angle = - ((90 + 28.44)) - initial_angle
        print(f"rot_angle: {rot_angle:.2f} degrees")
    
        # new_large_circle_center_xyz = rotate_point_in_yz_plane(tcp_xyz, large_circle_center_xyz, rot_angle)
        # print(f"pt2_world:{pt2_world}")
        # print(f"new_large_circle_center_xyz:{new_large_circle_center_xyz}")
        # dy = pt2_world[0] - new_large_circle_center_xyz[1]
        # dz = pt2_world[1] - new_large_circle_center_xyz[2]
        # print(f"dy: {dy:.2f} m, dz: {dz:.2f} m")


    user_input = input("move the robotic? (y/n)").lower()
    if user_input == 'n':
        print("Down!")
        exit()
    elif user_input == 'y':
        print("robotic move!")
    else:
        print("no valiad!")
        exit()

    # move robot
    if ROT:
        with URRobotController("192.168.25.15") as robot:

        # position_take_pic_euler = np.array([769.96, -253.86, 363.07, 180, -0.0, -90.0]) # x, y, z, rx, ry, rz
            position_take_pic_euler = np.array([0.76383, -0.24167, 0.36874, 180, -0.0, -90.0])
        # position_take_pic_euler[4] -= (28.44 - robot_rx_deg)
            position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='m')
            position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='m')

            # =========== Translation:: make circle centers aligned ===========

            position_take_pic_axis[0] -= 0.15
            robot.move_linear(position_take_pic_axis, 0.05, 0.05)

            position_take_pic_axis[0] += 0.15
            # position_take_pic_axis[1] += dy  # dy is worry - 0.015
            # position_take_pic_axis[2] += dz
            position_take_pic_axis[1] = -0.1 
            position_take_pic_axis[2] = 0.332
            Rx = position_take_pic_euler[3] 
            Ry = position_take_pic_euler[4] + (rot_angle)
            Rz = position_take_pic_euler[5]
            target_base_euler = np.array([
                position_take_pic_axis[0], position_take_pic_axis[1], position_take_pic_axis[2], 
                Rx, Ry, Rz])
            print(f"target_base_euler: {target_base_euler}")
            target_base_matrix = pose_6d_to_matrix(target_base_euler, unit='m')
            target_base_axis = matrix_to_axis_6d(target_base_matrix, unit='m')
            robot.move_linear(target_base_axis, 0.05, 0.05)

            # position = robot.get_tcp_pose()
            # position[1] = v_trans_base[1]
            # position[2] = v_trans_base[2]
            # robot.move_linear(position, 0.05, 0.05) 

        # robot.move_linear(position_take_pic_axis, 0.05, 0.05)
            # =========== Rotation:: make angle aligned =======================
            # Rx = position_take_pic_euler[3] 
            # Ry = position_take_pic_euler[4] - (28.44 - robot_rx_deg)
            # Rz = position_take_pic_euler[5]
            # target_base_euler = np.array([position_take_pic_axis[0], v_trans_base[1], v_trans_base[2], Rx, Ry, Rz])
            # target_base_matrix = pose_6d_to_matrix(target_base_euler, unit='m')
            # target_base_axis = matrix_to_axis_6d(target_base_matrix, unit='m')
            # robot.move_linear(target_base_axis, 0.05, 0.05)

            robot.disconnect()
    else:
        with URRobotController("192.168.25.15") as robot:
            position = robot.get_tcp_pose()
            position[0] -= 0.15
            robot.move_linear(position, 0.05, 0.05)

            position[0] += 0.12
            position[1] += d_world[0]
            position[2] += d_world[1]
            robot.move_linear(position, 0.05, 0.05) 
            robot.disconnect()


    # # ============ angle ==================
    # vis, angles = detect_lines(roi_gray_large)
    # cv2.imwrite("vis.png", vis)
    # rotated_endpoint = calculate_rotated_vector_endpoint(angles[0], (x1, y1))
    # robot_rx_deg, robot_rx_rad, _ = calculate_robot_rx_from_pixel_rotation(
    #     p_target_pix=rotated_endpoint, p_center_pix=(x1, y1), angle_a_deg=angles[0], M=R_cam_to_world) 
    # print(f"robot_rx: {robot_rx_deg:.2f} degrees, {robot_rx_rad:.2f} rad")
    # # ============ angle ==================

    # # ====================================== TEST ====================
    # a = np.array([769.96, -253.86, 363.07])
    # print(f"a:{a}")
    # a_uv_homo = np.linalg.inv(R_cam_to_world) @ a
    # print(f"a_uv:{a_uv_homo}")
    # a_uv = a_uv_homo[0:2]
    # img = cv2.imread(scene_path)
    # center_x, center_y = -a_uv[1], -a_uv[0]
    # print(f"center_x, center_y: {center_x, center_y}")
    # cv2.circle(img,(int(center_x), int(center_y)), radius=3, color=(0, 0, 255), thickness=100)
    # cv2.imwrite("TCP_UV.png", img)

    # a_trans = R_cam_to_world @ a_uv
    # print(f"a_trans:{a_trans}")
    # a_trans /= 1000
    # print(f"a_trans:{a_trans}")
    # # ====================================== TEST ====================

    # # move robot
    # with URRobotController("192.168.25.15") as robot:
        
    #     # position_take_pic_axis = robot.get_tcp_pose()
    #     position_take_pic_euler = np.array([0.76996, -0.25386, 0.36307, 180.0, -0.0, -90.0])
    #     position_take_pic_euler[4] -= (28.44 - robot_rx_deg)
    #     position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='m')
    #     position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='m')

    #     position_take_pic_axis[0] -= 0.1
    #     robot.move_linear(position_take_pic_axis, 0.1, 0.01)
    #     position_take_pic_axis[0] += 0.08
    #     position_take_pic_axis[1] += d_world[1]
    #     position_take_pic_axis[2] += d_world[2]
    #     robot.move_linear(position_take_pic_axis, 0.1, 0.01)

    #     robot.disconnect()

    # move robot
    # with URRobotController("192.168.25.15") as robot:
    #     cur_pose = robot.get_tcp_pose()
    #     cur_pose_translate = np.array([cur_pose[0], cur_pose[1], cur_pose[2]]) * 1000 # m -> mm
    #     print(f"cur_pose_translate:{cur_pose_translate}")
    #     cur_pose_uv_hom = np.linalg.inv(R_cam_to_world) @ cur_pose_translate
    #     print(f"cur_pose_uv_hom: {cur_pose_uv_hom}")
    #     cur_pose_uv = cur_pose_uv_hom[:2]
    #     _, _, v_new_base = calculate_robot_rx_from_pixel_rotation(
    #         p_target_pix=cur_pose_uv, p_center_pix=(x1, y1), angle_a_deg=angles[0], M=R_cam_to_world) 
    #     v_new_base /= 1000
    #     print(f"v_new_base: {v_new_base}")

        
    #     position_take_pic_euler = np.array([769.96, -253.86, 363.07, 180, -0.0, -90.0]) # x, y, z, rx, ry, rz
    #     position_take_pic_euler[3] -= robot_rx_deg
    #     position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='mm')
    #     position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='mm')

    #     # =========== Translation:: make circle centers aligned ===========
    #     cur_pose[0] -= 0.1
    #     robot.move_linear(cur_pose, 0.05, 0.05)

    #     cur_pose[0] += 0.08
    #     cur_pose[1] += d_world[1]
    #     cur_pose[2] += d_world[2]
    #     robot.move_linear(cur_pose, 0.05, 0.05)
    #     # =========== Rotation:: make angle aligned =======================
    #     target_pose = np.array([
    #         cur_pose[0], v_new_base[1], v_new_base[2], 
    #         position_take_pic_axis[3], position_take_pic_axis[4], position_take_pic_axis[5]])
    #     robot.move_linear(target_pose, 0.05, 0.05)

    #     robot.disconnect()
    
    

if __name__ == "__main__":
    main()
