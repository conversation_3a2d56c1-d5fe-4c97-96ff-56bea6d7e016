# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import numpy as np
import matplotlib.pyplot as plt
import PIL.Image
import argparse
from sam_predictor import Predictor


if __name__ == "__main__":

    parser = argparse.ArgumentParser()
    parser.add_argument("--image_encoder", type=str, default="data/mobile_sam_image_encoder_fp32.engine")
    parser.add_argument("--mask_decoder", type=str, default="data/mobile_sam_mask_decoder.engine")
    args = parser.parse_args()
        
    # Instantiate TensorRT predictor
    predictor = Predictor(
        args.image_encoder,
        args.mask_decoder
    )
    # # ======================= Box prompt =======================
    # Read image and run image encoder
    # image = PIL.Image.open("assets/dogs.jpg")
    image = PIL.Image.open("assets/image_0709.png").convert("RGB")
    predictor.set_image(image)

    # Segment using bounding box
    # bbox = [100, 100, 850, 759]  # x0, y0, x1, y1
    bbox = [794, 353, 850, 661]

    points = np.array([
        [bbox[0], bbox[1]],
        [bbox[2], bbox[3]]
    ])

    point_labels = np.array([2, 3])
    # # ===========================================================
    # ====================== point prompt ========================
    # Read image and run image encoder
    # image = PIL.Image.open("assets/image_0709.png").convert("RGB") 
    # predictor.set_image(image)

    # # Segment using bounding box
    # bbox = [1050, 622, 962, 692]  # x0, y0

    # points = np.array([
    #     [bbox[0], bbox[1]],
    #     [bbox[2], bbox[3]]
    # ])

    # point_labels = np.array([1,1])
    # ====================== point prompt ========================
    
    mask, _, _ = predictor.predict(points, point_labels)

    mask = (mask[0, 0] > 0).detach().cpu().numpy()
   
    mask_uint8 = mask.astype(np.uint8) * 255
    # 从 NumPy 数组创建 PIL 图像对象
    # 'L' 模式表示灰度图像
    pil_mask = PIL.Image.fromarray(mask_uint8, 'L')
    print(f"pil_mask.size: {pil_mask.size}")
    pil_mask.save("data/image_0709_mask.png")

    # Draw resykts
    plt.imshow(image)
    plt.imshow(mask, alpha=0.5)
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, 'g-')
    # plt.plot(bbox[0], bbox[1], 'go', markersize=5)
    # plt.plot(bbox[2], bbox[3], 'go', markersize=5)
   
    plt.savefig("data/image_0709_out.png")

