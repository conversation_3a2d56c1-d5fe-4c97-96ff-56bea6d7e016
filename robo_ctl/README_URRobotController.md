# URRobotController 使用说明

## 概述

`URRobotController` 是一个封装了 UR 机械臂控制和 Robotiq 夹爪操作的高级控制类。它简化了机械臂编程，提供了清晰的 API 和完善的错误处理机制。

## 特性

- **统一接口**: 封装 RTDE 控制和接收接口
- **夹爪集成**: 完整的 Robotiq 夹爪控制功能
- **错误处理**: 完善的异常处理和日志记录
- **上下文管理**: 支持 with 语句自动资源清理
- **类型提示**: 完整的类型注解，便于开发

## 快速开始

### 基本使用

```python
from robo_ctl.ur_robot_controller import URRobotController

# 使用上下文管理器（推荐）
with URRobotController("*************") as robot:
    # 激活夹爪
    robot.activate_gripper()
    robot.open_gripper()
    
    # 获取当前位置
    current_pose = robot.get_tcp_pose()
    print(f"Current pose: {current_pose}")
    
    # 移动到新位置
    target_pose = [0.3, 0.2, 0.4, 0, 3.14, 0]  # [x, y, z, rx*θ, ry*θ, rz*θ]
    robot.move_linear(target_pose, velocity=0.1, acceleration=0.05)
    
    # 关闭夹爪
    robot.close_gripper()
```

### 手动管理连接

```python
robot = URRobotController("*************")
try:
    # 执行操作...
    robot.activate_gripper()
    # ...
finally:
    robot.disconnect()
```

## API 参考

### 初始化

#### `__init__(robot_host: str)`

初始化机械臂控制器。

**参数:**
- `robot_host` (str): 机械臂的 IP 地址

**异常:**
- `ConnectionError`: 连接失败时抛出

### 夹爪控制

#### `activate_gripper() -> bool`
激活夹爪。首次使用前必须调用。

#### `open_gripper() -> bool`
打开夹爪。

#### `close_gripper() -> bool`
关闭夹爪。

#### `set_gripper_speed(speed: int) -> bool`
设置夹爪速度。

**参数:**
- `speed` (int): 速度百分比 [0-100]

#### `set_gripper_force(force: int) -> bool`
设置夹爪力度。

**参数:**
- `force` (int): 力度百分比 [0-100]

### 运动控制

#### `move_linear(pose: List[float], velocity: float, acceleration: float) -> bool`
执行直线运动。

**参数:**
- `pose` (List[float]): 目标位姿 [x, y, z, `rx*θ`, `ry*θ`, `rz*θ`]
- `velocity` (float): 工具速度 (m/s)
- `acceleration` (float): 工具加速度 (m/s²)

#### `move_joints(joint_positions: List[float], velocity: float, acceleration: float) -> bool`
执行关节运动。

**参数:**
- `joint_positions` (List[float]): 目标关节位置 (弧度)
- `velocity` (float): 关节速度 (rad/s)
- `acceleration` (float): 关节加速度 (rad/s²)

### 状态查询

#### `get_tcp_pose() -> Optional[List[float]]`
获取当前 TCP 位姿。

**返回:**
- `List[float]`: 当前位姿 [x, y, z, `rx*θ`, `ry*θ`, `rz*θ`] 或 None

#### `get_joint_positions() -> Optional[List[float]]`
获取当前关节位置。

**返回:**
- `List[float]`: 关节位置 (弧度) 或 None

#### `is_connected() -> bool`
检查连接状态。

### 安全控制

#### `stop_robot() -> bool`
立即停止机械臂运动。

#### `disconnect()`
断开连接并清理资源。

## 示例代码

### 基本抓取操作

```python
from robo_ctl.ur_robot_controller import URRobotController

def basic_pick_and_place():
    with URRobotController("*************") as robot:
        # 初始化夹爪
        robot.activate_gripper()
        robot.open_gripper()
        
        # 设置夹爪参数
        robot.set_gripper_speed(80)
        robot.set_gripper_force(60)
        
        # 获取当前位置
        home_pose = robot.get_tcp_pose()
        
        # 移动到抓取位置
        pick_pose = [0.3, 0.2, 0.15, 0, 3.14, 0]
        robot.move_linear(pick_pose, 0.1, 0.05)
        
        # 抓取
        robot.close_gripper()
        
        # 移动到放置位置
        place_pose = [0.3, -0.2, 0.15, 0, 3.14, 0]
        robot.move_linear(place_pose, 0.1, 0.05)
        
        # 释放
        robot.open_gripper()
        
        # 返回初始位置
        robot.move_linear(home_pose, 0.1, 0.05)

if __name__ == "__main__":
    basic_pick_and_place()
```

### 安全运行模式

```python
def safe_operation():
    try:
        with URRobotController("*************") as robot:
            # 检查连接
            if not robot.is_connected():
                print("Robot not connected!")
                return
            
            # 执行操作
            current_joints = robot.get_joint_positions()
            if current_joints:
                print(f"Current joints: {current_joints}")
            
            # 在出现异常时自动停止
            try:
                # 执行可能失败的操作
                robot.move_linear([0.5, 0.5, 0.5, 0, 0, 0], 0.1, 0.1)
            except Exception as e:
                print(f"Movement failed: {e}")
                robot.stop_robot()
                
    except ConnectionError:
        print("Failed to connect to robot")
    except Exception as e:
        print(f"Unexpected error: {e}")
```

## 迁移指南

### 从原始代码迁移

**原始代码:**
```python
# 旧方式
import rtde_control
import rtde_receive
from robo_ctl.robotiq_gripper_control import RobotiqGripper

ROBOT_HOST = "*************"
rtde_c = rtde_control.RTDEControlInterface(ROBOT_HOST)
rtde_r = rtde_receive.RTDEReceiveInterface(ROBOT_HOST)
gripper = RobotiqGripper(rtde_c)

gripper.activate()
gripper.open()
pose = rtde_r.getActualTCPPose()
rtde_c.moveL(pose, 0.1, 0.05)
gripper.close()
```

**新方式:**
```python
# 新方式
from robo_ctl.ur_robot_controller import URRobotController

with URRobotController("*************") as robot:
    robot.activate_gripper()
    robot.open_gripper()
    pose = robot.get_tcp_pose()
    robot.move_linear(pose, 0.1, 0.05)
    robot.close_gripper()
```

## 注意事项

1. **首次使用**: 必须先调用 `activate_gripper()` 才能使用夹爪功能
2. **资源管理**: 推荐使用 `with` 语句自动管理连接
3. **异常处理**: 所有方法都包含错误处理，建议检查返回值
4. **线程安全**: 该类不是线程安全的，多线程环境下需要额外同步
5. **网络延迟**: 网络延迟可能影响实时性能，建议在稳定网络环境下使用

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 IP 地址是否正确
   - 确认机械臂网络连接正常
   - 验证防火墙设置

2. **夹爪激活失败**
   - 检查夹爪是否正确连接
   - 确认夹爪固件版本兼容
   - 等待激活完成（约5秒）

3. **运动指令失败**
   - 检查目标位置是否在工作空间内
   - 验证速度和加速度参数合理性
   - 确认机械臂处于自动模式 