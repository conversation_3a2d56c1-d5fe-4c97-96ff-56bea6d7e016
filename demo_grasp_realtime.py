import PIL.Image
import numpy as np
import matplotlib.pyplot as plt
import cv2
import open3d as o3d
import torch
import copy
import time
import sys

from pathlib import Path
# Add preprocess directory to Python path so that `import models` resolves correctly
sys.path.append(str(Path(__file__).resolve().parent / 'preprocess'))
print(sys.path)

from preprocess.sam_predictor import Predictor
from preprocess.infer_wrap import YOLOv8TRTDetector
from preprocess.mask2cloud import mask_to_point_cloud
from utils import bbox2points, draw_bbox, subplot_notick, pose_6d_to_matrix, matrix_to_pose_6d, matrix_to_axis_6d

from pose_estimator import PoseEstimator
from robo_ctl.ur_robot_controller import URRobotController

def get_user_confirmation():
    """Get user confirmation to move the robot."""
    user_input = input("Move the robot? (y/n): ").lower()
    if user_input == 'n':
        print("Operation cancelled.")
        exit()
    elif user_input != 'y':
        print("Invalid input!")
        exit()
    print("Robot moving!")

def main():
    # Path to files
    template = 'data/templateA/templateA.ply' 
    color = 'data/1.png'
    depth = 'data/1.tif'

    # config infer mask
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    yolo_engine = 'preprocess/weights/yolov8_seatbelt_v1.engine'
    image_encoder = 'preprocess/weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'preprocess/weights/mobile_sam_mask_decoder_fp16.engine'

    # Initialize detector and sam predictor
    detector = YOLOv8TRTDetector(yolo_engine, device)
    sam_predictor = Predictor(device, image_encoder, mask_decoder)
    
    # yolo infer
    pil_image = PIL.Image.open(color).convert('RGB') # if color is 1 channel, convert to 3 channel
    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    detections = detector.predict(cv_image)
    
    N = len(detections['bboxes'])
    
    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    bbox = detections['bboxes'][0]
    
    # sam infer
    points, point_labels = bbox2points(bbox)
    sam_predictor.set_image(pil_image)
    mask, _, _ = sam_predictor.predict(points, point_labels)
    mask = (mask[0, 0] > 0).detach().cpu().numpy()
    mask = mask * 255
    
    if True:
        subplot_notick(2, N, 1)
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title("Detection")

        subplot_notick(2, N, 2)
        plt.imshow(pil_image)
        plt.imshow(mask, alpha=0.5)
        plt.title("Segmentation Mask")
        plt.subplots_adjust(wspace=0.05, hspace=0.2)
        plt.savefig('out.png', bbox_inches="tight")
       
    # estimate pose
    depth = cv2.imread(depth, cv2.IMREAD_ANYDEPTH) # depth unit: m

    # generate point cloud from rgb and depth dependant on mask
    rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

    camera_intrinsic = np.array([
        [1577.85595703, 0, 717.44384765],
        [0, 1578.40002441, 565.813415527],
        [0, 0, 1]   
    ]) # RVC left instrinsic

    pcd_scene = mask_to_point_cloud(rgb, depth, mask, camera_intrinsic)
    
    # estimation
    source_pcd = o3d.io.read_point_cloud(template)
    target_pcd = pcd_scene

    T_cam_to_world = np.array([
        [1, 0, 0, 0],
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [0, 0, 0, 1]
    ])
    source_pcd_world = copy.deepcopy(source_pcd)
    source_pcd_world.transform(T_cam_to_world)
    target_pcd_world = copy.deepcopy(target_pcd)
    target_pcd_world.transform(T_cam_to_world)

    # Initialize pose estimator
    pose_estimator = PoseEstimator(source_pcd_world, target_pcd_world, voxel_size=0.002)
    T_source_to_target = pose_estimator.run()
    pose_estimator.visualize_registration(save_dir="out/T1", visualize_merged_clouds=False)
    
    # gripper pose in robot base frame
    gripper_pose_in_source = np.array([207.96, 269.18, 195.29, 128.88, -1, 87.52]) # x, y, z, rx, ry, rz
    gripper_pose_in_source_matrix = pose_6d_to_matrix(gripper_pose_in_source, unit='mm')

    # gripper pose in target frame
    gripper_pose_in_target_matrix = T_source_to_target @ gripper_pose_in_source_matrix 
    gripper_pose_in_target_euler = matrix_to_pose_6d(gripper_pose_in_target_matrix, unit='m')
    print(f"gripper_pose_in_target_euler: {gripper_pose_in_target_euler}")

    gripper_pose_in_target_axis = matrix_to_axis_6d(gripper_pose_in_target_matrix, unit='m')
    print(f"gripper_pose_in_target_axis: {gripper_pose_in_target_axis}")


    get_user_confirmation()
    # move robot
    with URRobotController("*************") as robot:
        robot.activate_gripper()
        robot.open_gripper()

        gripper_pose_in_target_axis[0] -= 0.2
        robot.move_linear(gripper_pose_in_target_axis, 0.05, 0.05)
        gripper_pose_in_target_axis[0] += 0.2
        robot.move_linear(gripper_pose_in_target_axis, 0.05, 0.05)
        robot.close_gripper()

        gripper_pose_in_target_axis[2] += 0.4
        robot.move_linear(gripper_pose_in_target_axis, 0.05, 0.05)

        position_take_pic_euler = np.array([769.96, -253.86, 363.07, 180, -0.0, -90.0]) # x, y, z, rx, ry, rz
        position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='mm')
        position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='m')
        position_take_pic_axis[0] -= 0.2
        robot.move_linear(position_take_pic_axis, 0.05, 0.05)
        position_take_pic_axis[0] += 0.2
        robot.move_linear(position_take_pic_axis, 0.05, 0.05)

        robot.disconnect()

if __name__ == "__main__":
    main()