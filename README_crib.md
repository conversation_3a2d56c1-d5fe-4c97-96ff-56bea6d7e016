# 机械臂3x3位姿阵列运动控制脚本使用说明

## 概述

`crib.py` 是一个用于控制UR机械臂执行3x3位姿阵列运动的Python脚本。该脚本可以让机械臂以指定的中心位姿为基准，在Y和Z方向上生成一个3x3的位姿网格，并依次移动到这9个位姿点。

## 功能特性

- ✅ 支持6D位姿输入（轴角表示法）
- ✅ 保持X坐标和姿态角度不变
- ✅ 生成3x3位姿阵列（以中心点为基准）
- ✅ 安全的机械臂运动控制
- ✅ 完善的错误处理和状态检查
- ✅ 用户交互确认机制
- ✅ 详细的日志记录

## 系统要求

### 硬件要求
- Universal Robots UR系列机械臂
- 网络连接（以太网）

### 软件要求
- Python 3.6+
- ur_rtde库
- robo_ctl模块（项目中的机械臂控制模块）

## 安装和配置

1. 确保机械臂已正确连接并配置网络
2. 安装必要的Python依赖：
   ```bash
   pip install ur_rtde
   ```
3. 确保robo_ctl模块在Python路径中

## 使用方法

### 基本使用

```bash
python crib.py
```

### 编程接口使用

```python
from crib import RobotCribController

# 创建控制器
controller = RobotCribController("*************")

# 连接机械臂
if controller.connect():
    # 定义中心位姿和间隔
    center_pose = [0.3, 0.0, 0.4, 0.0, 3.14, 0.0]
    dy = 0.05  # Y方向间隔 5cm
    dz = 0.05  # Z方向间隔 5cm
    
    # 执行3x3位姿阵列运动
    success = controller.execute_crib_pattern(
        center_pose=center_pose,
        dy=dy,
        dz=dz,
        velocity=0.1,
        acceleration=0.05,
        pause_time=1.0
    )
    
    # 断开连接
    controller.disconnect()
```

## 参数说明

### 位姿格式
位姿使用6D向量表示：`[x, y, z, rx, ry, rz]`
- `x, y, z`: 位置坐标（米）
- `rx, ry, rz`: 姿态角度（弧度，轴角表示法）

### 运动参数
- `velocity`: 工具速度（m/s），推荐值：0.05-0.2
- `acceleration`: 工具加速度（m/s²），推荐值：0.05-0.1
- `pause_time`: 每个位姿点的停留时间（秒）

### 网格参数
- `dy`: Y方向间隔（米）
- `dz`: Z方向间隔（米）

## 3x3网格布局

脚本生成的3x3网格布局如下（从机械臂的角度看）：

```
Z轴方向（上下）
    ↑
    |
7   8   9
4   5   6  → Y轴方向（左右）
1   2   3
```

- 位姿点1: (y-dy, z-dz)
- 位姿点2: (y, z-dz)
- 位姿点3: (y+dy, z-dz)
- 位姿点4: (y-dy, z)
- 位姿点5: (y, z) - 中心点
- 位姿点6: (y+dy, z)
- 位姿点7: (y-dy, z+dz)
- 位姿点8: (y, z+dz)
- 位姿点9: (y+dy, z+dz)

## 安全注意事项

1. **工作空间检查**: 确保所有生成的位姿点都在机械臂的工作空间内
2. **碰撞检测**: 检查运动路径是否会与环境发生碰撞
3. **速度限制**: 使用适当的速度和加速度参数
4. **紧急停止**: 确保能够随时停止机械臂运动
5. **权限确认**: 确保机械臂处于远程控制模式

## 错误处理

脚本包含以下错误处理机制：

- **连接错误**: 检查机械臂网络连接
- **位姿验证**: 验证输入位姿格式
- **运动失败**: 检查每个运动指令的执行结果
- **异常捕获**: 捕获和记录所有异常

## 日志记录

脚本使用Python的logging模块记录详细信息：

- INFO级别: 正常操作信息
- WARNING级别: 警告信息
- ERROR级别: 错误信息

日志格式：`时间戳 - 级别 - 消息`

## 测试

使用测试脚本验证算法正确性：

```bash
python test_crib.py
```

测试内容包括：
- 位姿生成算法验证
- 边界情况测试
- 网格布局可视化

## 故障排除

### 常见问题

1. **连接失败**
   - 检查机械臂IP地址
   - 确认网络连接正常
   - 检查机械臂是否处于远程控制模式

2. **运动失败**
   - 检查位姿是否在工作空间内
   - 确认运动参数合理
   - 检查是否有碰撞风险

3. **位姿格式错误**
   - 确保位姿包含6个数值元素
   - 检查单位是否正确（米和弧度）

### 调试建议

1. 启用详细日志记录
2. 使用较小的间隔值进行测试
3. 先在仿真环境中验证
4. 逐步增加运动速度

## 扩展功能

可以考虑的扩展功能：

1. **可配置网格尺寸**: 支持NxN网格
2. **路径优化**: 优化访问顺序减少运动时间
3. **可视化界面**: 添加图形界面显示运动轨迹
4. **配置文件**: 支持从文件加载参数
5. **安全检查**: 增强的碰撞检测和工作空间验证

## 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: 1.0  
**更新日期**: 2025-01-28  
**作者**: AI Assistant 