import numpy as np
from scipy.spatial.transform import Rotation as R

import matplotlib.pyplot as plt
import PIL.Image

def pose_6d_to_matrix(pose_6d, unit='m'):
    """
    Convert a 6D pose vector (x, y, z, rx, ry, rz) to a 4x4 homogeneous transformation matrix.

    Args:
        pose_6d (list or np.ndarray): 6D pose vector [x, y, z, rx, ry, rz].
            - x, y, z: Translation components (unit: meters or millimeters, specified by `unit`).
            - rx, ry, rz: Rotation components (degrees, XYZ Euler angles).
        unit (str): Unit of translation components ('m' for meters, 'mm' for millimeters).

    Returns:
        np.ndarray: 4x4 homogeneous transformation matrix.

    Raises:
        ValueError: If input is invalid (e.g., not a 6D vector or invalid unit).

    Example:
        >>> pose_6d = [1.0, 2.0, 3.0, 30, 45, 60]
        >>> pose_6d_to_matrix(pose_6d, unit='m')
    """
    if len(pose_6d) != 6:
        raise ValueError("Input pose_6d must be a 6D vector [x, y, z, rx, ry, rz].")

    x, y, z, rx, ry, rz = pose_6d

    # Convert translation units if necessary
    if unit == 'mm':
        x, y, z = x / 1000.0, y / 1000.0, z / 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' for meters or 'mm' for millimeters.")

    # Convert Euler angles (degrees) to rotation matrix
    rotation = R.from_euler('xyz', [rx, ry, rz], degrees=True).as_matrix()

    # Construct 4x4 transformation matrix
    transform = np.eye(4)
    transform[:3, :3] = rotation
    transform[:3, 3] = [x, y, z]

    return transform

def matrix_to_pose_6d(matrix, unit='m'):
    """
    Convert a 4x4 homogeneous transformation matrix to a 6D pose vector (x, y, z, rx, ry, rz).

    Args:
        matrix (np.ndarray): 4x4 homogeneous transformation matrix.
        unit (str): Unit of translation components ('m' for meters, 'mm' for millimeters).

    Returns:
        np.ndarray: 6D pose vector [x, y, z, rx, ry, rz].
            - x, y, z: Translation components (unit: meters or millimeters, specified by `unit`).
            - rx, ry, rz: Rotation components (degrees, XYZ Euler angles).

    Raises:
        ValueError: If input is not a valid 4x4 matrix.

    Example:
        >>> matrix = np.array([[1, 0, 0, 1.0],
                              [0, 1, 0, 2.0],
                              [0, 0, 1, 3.0],
                              [0, 0, 0, 1]])
        >>> matrix_to_pose_6d(matrix, unit='m')
    """
    if matrix.shape != (4, 4):
        raise ValueError("Input must be a 4x4 matrix.")

    # Extract translation components
    x, y, z = matrix[:3, 3]

    # Convert translation units if necessary
    if unit == 'mm':
        x, y, z = x * 1000.0, y * 1000.0, z * 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' for meters or 'mm' for millimeters.")

    # Extract rotation matrix and convert to Euler angles (degrees)
    rotation = matrix[:3, :3]
    rx, ry, rz = R.from_matrix(rotation).as_euler('xyz', degrees=True)

    return np.array([x, y, z, rx, ry, rz])


def matrix_to_axis_6d(matrix, unit='m'):
    """
    [Fixed] Convert a 4x4 transformation matrix to a 6D pose represented by rotation vector.
    (This representation is commonly used in UR robots)

    Args:
        matrix (np.ndarray): 4x4 homogeneous transformation matrix.
        unit (str): Unit of translation components ('m' for meters, 'mm' for millimeters).

    Returns:
        np.ndarray: 6D pose vector [x, y, z, ax, ay, az] (rotation vector).
    """
    if matrix.shape != (4, 4):
        raise ValueError("Input must be a 4x4 matrix.")
        
    # Use scipy to safely extract rotation vector from matrix
    # .as_rotvec() directly converts matrix to axis-angle representation and handles edge cases
    rotation_vector = R.from_matrix(matrix[:3, :3]).as_rotvec()
    
    # Extract translation components
    x, y, z = matrix[:3, 3]
    
    # Convert units from meters to millimeters if needed
    if unit == 'mm':
        x, y, z = x * 1000.0, y * 1000.0, z * 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' or 'mm'.")
        
    return np.array([x, y, z, rotation_vector[0], rotation_vector[1], rotation_vector[2]])


def axis_6d_to_matrix(pose_6d_axis, unit='m'):
    """
    [New] Convert a 6D pose vector represented by rotation vector to a 4x4 homogeneous transformation matrix.

    Args:
        pose_6d_axis (list or np.ndarray): 6D pose vector [x, y, z, ax, ay, az].
            - x, y, z: Translation components.
            - ax, ay, az: Rotation components represented as rotation vector.
        unit (str): Unit of translation components ('m' for meters, 'mm' for millimeters).

    Returns:
        np.ndarray: 4x4 homogeneous transformation matrix.
    """
    if len(pose_6d_axis) != 6:
        raise ValueError("Input must be a 6D vector [x, y, z, ax, ay, az].")

    x, y, z = pose_6d_axis[:3]
    rotation_vector = pose_6d_axis[3:]

    # Convert units from millimeters to meters if needed
    if unit == 'mm':
        x, y, z = x / 1000.0, y / 1000.0, z / 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' or 'mm'.")

    # Convert rotation vector to rotation matrix
    rotation_matrix = R.from_rotvec(rotation_vector).as_matrix()

    # Construct 4x4 transformation matrix
    transform_matrix = np.eye(4)
    transform_matrix[:3, :3] = rotation_matrix
    transform_matrix[:3, 3] = [x, y, z]

    return transform_matrix


def subplot_notick(a, b, c):
    """Create subplot without ticks"""
    ax = plt.subplot(a, b, c)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.axis('off')


def bbox2points(bbox):
    """Convert bounding box to points for SAM"""
    points = np.array([
        [bbox[0], bbox[1]],  # top-left
        [bbox[2], bbox[3]]   # bottom-right
    ])

    point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
    return points, point_labels


def draw_bbox(bbox, color='g'):
    """Draw bounding box on the plot"""
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, color + '-')


if __name__ == "__main__":
    pose_6d = [1.0, 2.0, 3.0, 30, 45, 60]
    np.set_printoptions(precision=3, suppress=True, linewidth=100)
    matrix = pose_6d_to_matrix(pose_6d, unit='m')
    print(matrix)
    pose_6d_re = matrix_to_pose_6d(matrix, unit='m')
    print(pose_6d_re)

    position_take_pic_euler = np.array([769.96, -253.86, 363.07, 180, -0.0, -90.0])
    position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='mm')
    position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='mm')
    print(f"position_take_pic_axis: {position_take_pic_axis}")
    # position_take_pic_axis should be [769.96, -253.86, 363.07, 2.221, -2.221, 0]
    # but this result is [769.96, -253.86, 363.07, 1.571, -1.571, 0.000]